Component: ARM Compiler 6.7 Tool: armlink [5c9ef700]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler) for DMA1_Channel7_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx_1.o(.text.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.main) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    main.o(.text.main) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    main.o(.text.main) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    main.o(.text.main) refers to config_manager.o(.text.Config_Init) for Config_Init
    main.o(.text.main) refers to system_status.o(.text.System_Status_Init) for System_Status_Init
    main.o(.text.main) refers to system_monitor.o(.text.System_Monitor_Init) for System_Monitor_Init
    main.o(.text.main) refers to oled_display.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to key_handler.o(.text.Key_Init) for Key_Init
    main.o(.text.main) refers to modbus_master.o(.text.Modbus_Master_Init) for Modbus_Master_Init
    main.o(.text.main) refers to modbus_slave.o(.text.Modbus_Slave_Init) for Modbus_Slave_Init
    main.o(.text.main) refers to modbus_extended.o(.text.Modbus_Extended_Init) for Modbus_Extended_Init
    main.o(.text.main) refers to system_status.o(.text.Watchdog_Init) for Watchdog_Init
    main.o(.text.main) refers to system_status.o(.text.System_Status_Update) for System_Status_Update
    main.o(.text.main) refers to system_monitor.o(.text.System_Monitor_Update) for System_Monitor_Update
    main.o(.text.main) refers to key_handler.o(.text.Key_Process) for Key_Process
    main.o(.text.main) refers to modbus_slave.o(.text.Modbus_Slave_Process) for Modbus_Slave_Process
    main.o(.text.main) refers to modbus_master.o(.text.Modbus_Master_Process) for Modbus_Master_Process
    main.o(.text.main) refers to config_manager.o(.text.Apply_Data_Corrections) for Apply_Data_Corrections
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    main.o(.text.main) refers to modbus_extended.o(.text.Update_SF6_Data_From_Modbus) for Update_SF6_Data_From_Modbus
    main.o(.text.main) refers to oled_display.o(.text.OLED_Display_Process) for OLED_Display_Process
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    main.o(.text.main) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    main.o(.text.main) refers to system_status.o(.bss.g_system_state) for g_system_state
    main.o(.text.main) refers to main.o(.bss.main.last_sf6_update) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.SystemClock_Config) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f1xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f1xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f1xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f1xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f1xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f1xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f1xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f1xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.SysTick_Handler) refers to stm32f1xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(.text.SysTick_Handler) refers to key_handler.o(.text.Key_Scan_In_ISR) for Key_Scan_In_ISR
    stm32f1xx_it.o(.text.SysTick_Handler) refers to stm32f1xx_it.o(.bss.SysTick_Handler.key_scan_counter) for [Anonymous Symbol]
    stm32f1xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f1xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(.text.USART1_IRQHandler) refers to main.o(.bss..L_MergedGlobals) for huart1
    stm32f1xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f1xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(.text.USART2_IRQHandler) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    stm32f1xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f1xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.1) for hdma_usart1_tx
    stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel4_IRQHandler) refers to stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals) for hdma_usart1_rx
    stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel5_IRQHandler) refers to stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.2) for hdma_usart2_rx
    stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel6_IRQHandler) refers to stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.3) for hdma_usart2_tx
    stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel7_IRQHandler) refers to stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f1xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_ADC_MspDeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32f1xx_hal_msp.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.2) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.3) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    config_manager.o(.text.Config_Init) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Config_Init) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Config_Init) refers to config_manager.o(.text.Config_Init) for [Anonymous Symbol]
    config_manager.o(.text.Config_Load) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Config_Load) refers to config_manager.o(.text.Config_Load) for [Anonymous Symbol]
    config_manager.o(.text.Config_Save) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    config_manager.o(.text.Config_Save) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    config_manager.o(.text.Config_Save) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) for HAL_FLASH_Program
    config_manager.o(.text.Config_Save) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock) for HAL_FLASH_Lock
    config_manager.o(.text.Config_Save) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Config_Save) refers to config_manager.o(.text.Config_Save) for [Anonymous Symbol]
    config_manager.o(.text.Config_Validate) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Config_Validate) refers to config_manager.o(.text.Config_Validate) for [Anonymous Symbol]
    config_manager.o(.text.Validate_Slave_Address) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Validate_Slave_Address) refers to config_manager.o(.text.Validate_Slave_Address) for [Anonymous Symbol]
    config_manager.o(.text.Set_Slave_Address) refers to config_manager.o(.text.Validate_Slave_Address) for Validate_Slave_Address
    config_manager.o(.text.Set_Slave_Address) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Set_Slave_Address) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Set_Slave_Address) refers to config_manager.o(.text.Set_Slave_Address) for [Anonymous Symbol]
    config_manager.o(.text.Set_Slave_Baudrate) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Set_Slave_Baudrate) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Set_Slave_Baudrate) refers to config_manager.o(.text.Set_Slave_Baudrate) for [Anonymous Symbol]
    config_manager.o(.text.Set_SF6_Threshold) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    config_manager.o(.text.Set_SF6_Threshold) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Set_SF6_Threshold) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Set_SF6_Threshold) refers to config_manager.o(.text.Set_SF6_Threshold) for [Anonymous Symbol]
    config_manager.o(.text.Set_Pressure_Correction) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    config_manager.o(.text.Set_Pressure_Correction) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Set_Pressure_Correction) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Set_Pressure_Correction) refers to config_manager.o(.text.Set_Pressure_Correction) for [Anonymous Symbol]
    config_manager.o(.text.Set_Moisture_Correction) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    config_manager.o(.text.Set_Moisture_Correction) refers to config_manager.o(.text.Config_Save) for Config_Save
    config_manager.o(.text.Set_Moisture_Correction) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.ARM.exidx.text.Set_Moisture_Correction) refers to config_manager.o(.text.Set_Moisture_Correction) for [Anonymous Symbol]
    config_manager.o(.text.Apply_Data_Corrections) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    config_manager.o(.text.Apply_Data_Corrections) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpeq
    config_manager.o(.text.Apply_Data_Corrections) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    config_manager.o(.text.Apply_Data_Corrections) refers to config_manager.o(.bss.g_config) for g_config
    config_manager.o(.text.Apply_Data_Corrections) refers to config_manager.o(.bss.corrected_data) for corrected_data
    config_manager.o(.text.Apply_Data_Corrections) refers to config_manager.o(.bss.modbus_data) for modbus_data
    config_manager.o(.ARM.exidx.text.Apply_Data_Corrections) refers to config_manager.o(.text.Apply_Data_Corrections) for [Anonymous Symbol]
    config_manager.o(.text.Get_Corrected_Register_Value) refers to config_manager.o(.bss.corrected_data) for corrected_data
    config_manager.o(.ARM.exidx.text.Get_Corrected_Register_Value) refers to config_manager.o(.text.Get_Corrected_Register_Value) for [Anonymous Symbol]
    key_handler.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Key_Scan_In_ISR) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Key_Scan_In_ISR) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_handler.o(.text.Key_Scan_In_ISR) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Key_Scan_In_ISR) refers to key_handler.o(.bss..L_MergedGlobals.31) for [Anonymous Symbol]
    key_handler.o(.text.Key_Scan_In_ISR) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR) refers to key_handler.o(.text.Key_Scan_In_ISR) for [Anonymous Symbol]
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Key_Scan) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Key_Scan) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Key_Scan) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Key_Scan) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Key_Scan) refers to key_handler.o(.text.Key_Scan) for [Anonymous Symbol]
    key_handler.o(.text.Key_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Key_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Key_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Key_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Key_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Key_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Key_Init) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Key_Init) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Key_Init) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Key_Init) refers to key_handler.o(.text.Reset_Setting_Variables) for Reset_Setting_Variables
    key_handler.o(.text.Key_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    key_handler.o(.text.Key_Init) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.text.Key_Init) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Key_Init) refers to key_handler.o(.bss..L_MergedGlobals.31) for [Anonymous Symbol]
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Key_Init) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Key_Init) refers to key_handler.o(.text.Key_Init) for [Anonymous Symbol]
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Reset_Setting_Variables) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Reset_Setting_Variables) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Reset_Setting_Variables) refers to config_manager.o(.bss.g_config) for g_config
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Reset_Setting_Variables) refers to key_handler.o(.text.Reset_Setting_Variables) for [Anonymous Symbol]
    key_handler.o(.text.Key_Process) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Key_Process) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Key_Process) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Key_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Key_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Key_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Key_Process) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Key_Process) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Key_Process) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Key_Process) refers to key_handler.o(.text.Process_Advanced_Setting_Menu) for Process_Advanced_Setting_Menu
    key_handler.o(.text.Key_Process) refers to key_handler.o(.text.Process_Password_Menu) for Process_Password_Menu
    key_handler.o(.text.Key_Process) refers to key_handler.o(.text.Process_Slave_Setting_Menu) for Process_Slave_Setting_Menu
    key_handler.o(.text.Key_Process) refers to key_handler.o(.text.Process_Master_Setting_Menu) for Process_Master_Setting_Menu
    key_handler.o(.text.Key_Process) refers to key_handler.o(.text.Update_Menu_Display) for Update_Menu_Display
    key_handler.o(.text.Key_Process) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Key_Process) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Key_Process) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Key_Process) refers to key_handler.o(.text.Key_Process) for [Anonymous Symbol]
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Process_Password_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Process_Password_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Process_Password_Menu) refers to key_handler.o(.text.Reset_Setting_Variables) for Reset_Setting_Variables
    key_handler.o(.text.Process_Password_Menu) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Process_Password_Menu) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Process_Password_Menu) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Process_Password_Menu) refers to key_handler.o(.text.Process_Password_Menu) for [Anonymous Symbol]
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Process_Slave_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to config_manager.o(.text.Config_Save) for Config_Save
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to config_manager.o(.bss.g_config) for g_config
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to key_handler.o(.rodata.cst16) for .Lswitch.table.29
    key_handler.o(.text.Process_Slave_Setting_Menu) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu) refers to key_handler.o(.text.Process_Slave_Setting_Menu) for [Anonymous Symbol]
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Process_Master_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Process_Master_Setting_Menu) refers to config_manager.o(.text.Config_Save) for Config_Save
    key_handler.o(.text.Process_Master_Setting_Menu) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Process_Master_Setting_Menu) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Process_Master_Setting_Menu) refers to config_manager.o(.bss.g_config) for g_config
    key_handler.o(.text.Process_Master_Setting_Menu) refers to key_handler.o(.rodata.cst16) for .Lswitch.table.29
    key_handler.o(.text.Process_Master_Setting_Menu) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu) refers to key_handler.o(.text.Process_Master_Setting_Menu) for [Anonymous Symbol]
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to config_manager.o(.text.Config_Save) for Config_Save
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Process_Advanced_Setting_Menu) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu) refers to key_handler.o(.text.Process_Advanced_Setting_Menu) for [Anonymous Symbol]
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Update_Menu_Display) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Update_Menu_Display) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Update_Menu_Display) refers to oled_display.o(.text.OLED_Clear) for OLED_Clear
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.text.Display_Data_Screen) for Display_Data_Screen
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.text.Display_Setting_Screen) for Display_Setting_Screen
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.text.Display_Address_Screen) for Display_Address_Screen
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.text.Display_Startup_Screen) for Display_Startup_Screen
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.text.Display_Password_Screen) for Display_Password_Screen
    key_handler.o(.text.Update_Menu_Display) refers to oled_display.o(.text.OLED_FrameBuffer_Update) for OLED_FrameBuffer_Update
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Update_Menu_Display) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Update_Menu_Display) refers to key_handler.o(.text.Update_Menu_Display) for [Anonymous Symbol]
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Display_Startup_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Display_Startup_Screen) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Display_Startup_Screen) refers to key_handler.o(.text.Display_Startup_Screen) for [Anonymous Symbol]
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Display_Data_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Display_Data_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Display_Data_Screen) refers to noretval__2sprintf.o(.text) for __2sprintf
    key_handler.o(.text.Display_Data_Screen) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    key_handler.o(.text.Display_Data_Screen) refers to system_monitor.o(.text.System_Get_Communication_Status) for System_Get_Communication_Status
    key_handler.o(.text.Display_Data_Screen) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    key_handler.o(.text.Display_Data_Screen) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    key_handler.o(.text.Display_Data_Screen) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    key_handler.o(.text.Display_Data_Screen) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    key_handler.o(.text.Display_Data_Screen) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    key_handler.o(.text.Display_Data_Screen) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.text.Display_Data_Screen) refers to config_manager.o(.bss.corrected_data) for corrected_data
    key_handler.o(.text.Display_Data_Screen) refers to config_manager.o(.bss.coil_data) for coil_data
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Display_Data_Screen) refers to key_handler.o(.text.Display_Data_Screen) for [Anonymous Symbol]
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Display_Address_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Display_Address_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Display_Address_Screen) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.text.Display_Address_Screen) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    key_handler.o(.text.Display_Address_Screen) refers to config_manager.o(.bss.g_config) for g_config
    key_handler.o(.text.Display_Address_Screen) refers to noretval__2sprintf.o(.text) for __2sprintf
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Display_Address_Screen) refers to key_handler.o(.text.Display_Address_Screen) for [Anonymous Symbol]
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Display_Password_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Display_Password_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Display_Password_Screen) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    key_handler.o(.text.Display_Password_Screen) refers to noretval__2sprintf.o(.text) for __2sprintf
    key_handler.o(.text.Display_Password_Screen) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Display_Password_Screen) refers to key_handler.o(.text.Display_Password_Screen) for [Anonymous Symbol]
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.text.Display_Setting_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.text.Display_Setting_Screen) refers to noretval__2sprintf.o(.text) for __2sprintf
    key_handler.o(.text.Display_Setting_Screen) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    key_handler.o(.text.Display_Setting_Screen) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    key_handler.o(.text.Display_Setting_Screen) refers to key_handler.o(.bss.current_menu) for current_menu
    key_handler.o(.text.Display_Setting_Screen) refers to key_handler.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    key_handler.o(.text.Display_Setting_Screen) refers to key_handler.o(.rodata.cst16) for .Lswitch.table.29
    key_handler.o(.text.Display_Setting_Screen) refers to key_handler.o(.rodata.str1.1) for .L.str.26
    key_handler.o(.text.Display_Setting_Screen) refers to key_handler.o(.rodata..Lswitch.table.30) for [Anonymous Symbol]
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.ARM.exidx.text.Display_Setting_Screen) refers to key_handler.o(.text.Display_Setting_Screen) for [Anonymous Symbol]
    key_handler.o(.bss.current_menu) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.bss.current_menu) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.bss.current_menu) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.bss.current_menu) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.bss.current_menu) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.bss.current_menu) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.bss.current_menu) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.bss.current_menu) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.rodata.str1.1) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.rodata.str1.1) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.rodata.cst16) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.rodata.cst16) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.rodata.cst16) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.rodata.cst16) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.rodata.cst16) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.rodata.cst16) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.rodata.cst16) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.rodata.cst16) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.rodata..Lswitch.table.30) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.rodata..Lswitch.table.30) refers to key_handler.o(.rodata.str1.1) for .L.str.26
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.bss..L_MergedGlobals) refers (Special) to _printf_str.o(.text) for _printf_str
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_handler.o(.bss..L_MergedGlobals.31) refers (Special) to _printf_str.o(.text) for _printf_str
    modbus_master.o(.text.Modbus_Master_Init) refers to modbus_master.o(.text.RS485_Set_Mode) for RS485_Set_Mode
    modbus_master.o(.ARM.exidx.text.Modbus_Master_Init) refers to modbus_master.o(.text.Modbus_Master_Init) for [Anonymous Symbol]
    modbus_master.o(.text.RS485_Set_Mode) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    modbus_master.o(.ARM.exidx.text.RS485_Set_Mode) refers to modbus_master.o(.text.RS485_Set_Mode) for [Anonymous Symbol]
    modbus_master.o(.text.Modbus_Master_Process) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.text.RS485_Set_Mode) for RS485_Set_Mode
    modbus_master.o(.text.Modbus_Master_Process) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_master.o(.text.Modbus_Master_Process) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    modbus_master.o(.text.Modbus_Master_Process) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) for HAL_UART_Receive
    modbus_master.o(.text.Modbus_Master_Process) refers to system_monitor.o(.text.System_Set_Communication_Status) for System_Set_Communication_Status
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.text.Handle_Communication_Error) for Handle_Communication_Error
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.text.Validate_Response) for Validate_Response
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.text.Parse_Response_Data) for Parse_Response_Data
    modbus_master.o(.text.Modbus_Master_Process) refers to config_manager.o(.bss.g_config) for g_config
    modbus_master.o(.text.Modbus_Master_Process) refers to modbus_master.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_master.o(.text.Modbus_Master_Process) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    modbus_master.o(.text.Modbus_Master_Process) refers to system_monitor.o(.bss.g_system_status) for g_system_status
    modbus_master.o(.ARM.exidx.text.Modbus_Master_Process) refers to modbus_master.o(.text.Modbus_Master_Process) for [Anonymous Symbol]
    modbus_master.o(.ARM.exidx.text.Modbus_CRC16) refers to modbus_master.o(.text.Modbus_CRC16) for [Anonymous Symbol]
    modbus_master.o(.text.Validate_Response) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_master.o(.text.Validate_Response) refers to config_manager.o(.bss.g_config) for g_config
    modbus_master.o(.ARM.exidx.text.Validate_Response) refers to modbus_master.o(.text.Validate_Response) for [Anonymous Symbol]
    modbus_master.o(.text.Parse_Response_Data) refers to config_manager.o(.bss.modbus_data) for modbus_data
    modbus_master.o(.ARM.exidx.text.Parse_Response_Data) refers to modbus_master.o(.text.Parse_Response_Data) for [Anonymous Symbol]
    modbus_master.o(.text.Handle_Communication_Error) refers to rt_memclr.o(.text) for __aeabi_memclr
    modbus_master.o(.text.Handle_Communication_Error) refers to system_monitor.o(.text.System_Set_Communication_Status) for System_Set_Communication_Status
    modbus_master.o(.text.Handle_Communication_Error) refers to modbus_master.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_master.o(.text.Handle_Communication_Error) refers to system_monitor.o(.bss.g_system_status) for g_system_status
    modbus_master.o(.text.Handle_Communication_Error) refers to config_manager.o(.bss.modbus_data) for modbus_data
    modbus_master.o(.ARM.exidx.text.Handle_Communication_Error) refers to modbus_master.o(.text.Handle_Communication_Error) for [Anonymous Symbol]
    modbus_master.o(.ARM.exidx.text.Modbus_Master_Process_Response) refers to modbus_master.o(.text.Modbus_Master_Process_Response) for [Anonymous Symbol]
    modbus_slave.o(.text.Modbus_Slave_Init) refers to uart_dma.o(.text.UART_DMA_Init) for UART_DMA_Init
    modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Init) refers to modbus_slave.o(.text.Modbus_Slave_Init) for [Anonymous Symbol]
    modbus_slave.o(.text.RS485_Slave_Set_Mode) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    modbus_slave.o(.ARM.exidx.text.RS485_Slave_Set_Mode) refers to modbus_slave.o(.text.RS485_Slave_Set_Mode) for [Anonymous Symbol]
    modbus_slave.o(.text.Modbus_Slave_Process) refers to uart_dma.o(.text.UART_DMA_Process) for UART_DMA_Process
    modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Process) refers to modbus_slave.o(.text.Modbus_Slave_Process) for [Anonymous Symbol]
    modbus_slave.o(.text.Modbus_Slave_Process_Frame) refers to modbus_slave.o(.text.Process_Modbus_Frame) for Process_Modbus_Frame
    modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Process_Frame) refers to modbus_slave.o(.text.Modbus_Slave_Process_Frame) for [Anonymous Symbol]
    modbus_slave.o(.text.Process_Modbus_Frame) refers to modbus_extended.o(.text.Modbus_Extended_Process) for Modbus_Extended_Process
    modbus_slave.o(.text.Process_Modbus_Frame) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_slave.o(.text.Process_Modbus_Frame) refers to modbus_slave.o(.text.Process_Read_Coils) for Process_Read_Coils
    modbus_slave.o(.text.Process_Modbus_Frame) refers to modbus_slave.o(.text.Process_Write_Single_Register) for Process_Write_Single_Register
    modbus_slave.o(.text.Process_Modbus_Frame) refers to modbus_slave.o(.text.Process_Read_Holding_Registers) for Process_Read_Holding_Registers
    modbus_slave.o(.text.Process_Modbus_Frame) refers to uart_dma.o(.text.UART1_Send_Data) for UART1_Send_Data
    modbus_slave.o(.text.Process_Modbus_Frame) refers to config_manager.o(.bss.g_config) for g_config
    modbus_slave.o(.ARM.exidx.text.Process_Modbus_Frame) refers to modbus_slave.o(.text.Process_Modbus_Frame) for [Anonymous Symbol]
    modbus_slave.o(.text.Process_Read_Coils) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_slave.o(.text.Process_Read_Coils) refers to system_monitor.o(.text.System_Get_Communication_Status) for System_Get_Communication_Status
    modbus_slave.o(.text.Process_Read_Coils) refers to config_manager.o(.bss.coil_data) for coil_data
    modbus_slave.o(.ARM.exidx.text.Process_Read_Coils) refers to modbus_slave.o(.text.Process_Read_Coils) for [Anonymous Symbol]
    modbus_slave.o(.text.Process_Read_Holding_Registers) refers to config_manager.o(.text.Get_Corrected_Register_Value) for Get_Corrected_Register_Value
    modbus_slave.o(.text.Process_Read_Holding_Registers) refers to modbus_slave.o(.text.Read_Config_Register) for Read_Config_Register
    modbus_slave.o(.text.Process_Read_Holding_Registers) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_slave.o(.ARM.exidx.text.Process_Read_Holding_Registers) refers to modbus_slave.o(.text.Process_Read_Holding_Registers) for [Anonymous Symbol]
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Set_Slave_Address) for Set_Slave_Address
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Config_Save) for Config_Save
    modbus_slave.o(.text.Process_Write_Single_Register) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    modbus_slave.o(.text.Process_Write_Single_Register) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Set_SF6_Threshold) for Set_SF6_Threshold
    modbus_slave.o(.text.Process_Write_Single_Register) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Set_Moisture_Correction) for Set_Moisture_Correction
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Set_Slave_Baudrate) for Set_Slave_Baudrate
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.text.Set_Pressure_Correction) for Set_Pressure_Correction
    modbus_slave.o(.text.Process_Write_Single_Register) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_slave.o(.text.Process_Write_Single_Register) refers to config_manager.o(.bss.g_config) for g_config
    modbus_slave.o(.ARM.exidx.text.Process_Write_Single_Register) refers to modbus_slave.o(.text.Process_Write_Single_Register) for [Anonymous Symbol]
    modbus_slave.o(.text.Read_Config_Register) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    modbus_slave.o(.text.Read_Config_Register) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    modbus_slave.o(.text.Read_Config_Register) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    modbus_slave.o(.text.Read_Config_Register) refers to config_manager.o(.bss.g_config) for g_config
    modbus_slave.o(.ARM.exidx.text.Read_Config_Register) refers to modbus_slave.o(.text.Read_Config_Register) for [Anonymous Symbol]
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to modbus_slave.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to modbus_slave.o(.bss..L_MergedGlobals.2) for [Anonymous Symbol]
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to modbus_slave.o(.bss.rx_buffer) for rx_buffer
    modbus_slave.o(.text.HAL_UART_RxCpltCallback) refers to main.o(.bss..L_MergedGlobals) for huart1
    modbus_slave.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to modbus_slave.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    oled_display.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.HAL_Delay_us) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.HAL_Delay_us) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.HAL_Delay_us) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.HAL_Delay_us) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.HAL_Delay_us) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.HAL_Delay_us) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.HAL_Delay_us) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.HAL_Delay_us) refers to oled_display.o(.text.HAL_Delay_us) for [Anonymous Symbol]
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_WriteCmd) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_WriteCmd) refers to oled_display.o(.text.I2C_Start) for I2C_Start
    oled_display.o(.text.OLED_WriteCmd) refers to oled_display.o(.text.I2C_Stop) for I2C_Stop
    oled_display.o(.text.OLED_WriteCmd) refers to oled_display.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_WriteCmd) refers to oled_display.o(.text.OLED_WriteCmd) for [Anonymous Symbol]
    oled_display.o(.text.I2C_Start) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.I2C_Start) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.I2C_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.I2C_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.I2C_Start) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.I2C_Start) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.I2C_Start) refers to oled_display.o(.text.HAL_Delay_us) for HAL_Delay_us
    oled_display.o(.text.I2C_Start) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.I2C_Start) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.I2C_Start) refers to oled_display.o(.text.I2C_Start) for [Anonymous Symbol]
    oled_display.o(.text.I2C_WriteByte) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.I2C_WriteByte) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.I2C_WriteByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.I2C_WriteByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.I2C_WriteByte) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.I2C_WriteByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.I2C_WriteByte) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled_display.o(.text.I2C_WriteByte) refers to oled_display.o(.text.HAL_Delay_us) for HAL_Delay_us
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.I2C_WriteByte) refers to oled_display.o(.text.I2C_WriteByte) for [Anonymous Symbol]
    oled_display.o(.text.I2C_Stop) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.I2C_Stop) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.I2C_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.I2C_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.I2C_Stop) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.I2C_Stop) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.I2C_Stop) refers to oled_display.o(.text.HAL_Delay_us) for HAL_Delay_us
    oled_display.o(.text.I2C_Stop) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.I2C_Stop) refers to oled_display.o(.text.I2C_Stop) for [Anonymous Symbol]
    oled_display.o(.text.OLED_WriteData) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_WriteData) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_WriteData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_WriteData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_WriteData) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_WriteData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_WriteData) refers to oled_display.o(.text.I2C_Start) for I2C_Start
    oled_display.o(.text.OLED_WriteData) refers to oled_display.o(.text.I2C_Stop) for I2C_Stop
    oled_display.o(.text.OLED_WriteData) refers to oled_display.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_WriteData) refers to oled_display.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled_display.o(.text.OLED_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_Init) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    oled_display.o(.text.OLED_Init) refers to oled_display.o(.text.OLED_FrameBuffer_Init) for OLED_FrameBuffer_Init
    oled_display.o(.text.OLED_Init) refers to oled_display.o(.text.OLED_WriteCmd) for OLED_WriteCmd
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_Init) refers to oled_display.o(.text.OLED_Init) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled_display.o(.text.OLED_FrameBuffer_Init) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    oled_display.o(.text.OLED_FrameBuffer_Init) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init) refers to oled_display.o(.text.OLED_FrameBuffer_Init) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled_display.o(.text.OLED_FrameBuffer_Clear) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear) refers to oled_display.o(.text.OLED_FrameBuffer_Clear) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_ClearArea) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea) refers to oled_display.o(.text.OLED_FrameBuffer_ClearArea) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_SetPixel) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel) refers to oled_display.o(.text.OLED_FrameBuffer_SetPixel) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_GetPixel) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel) refers to oled_display.o(.text.OLED_FrameBuffer_GetPixel) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_MarkDirty) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty) refers to oled_display.o(.text.OLED_FrameBuffer_MarkDirty) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh) refers to oled_display.o(.text.OLED_FrameBuffer_ForceRefresh) for [Anonymous Symbol]
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_FrameBuffer_Update) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_FrameBuffer_Update) refers to oled_display.o(.text.OLED_SetDisplayWindow) for OLED_SetDisplayWindow
    oled_display.o(.text.OLED_FrameBuffer_Update) refers to oled_display.o(.text.OLED_WriteDataBurst) for OLED_WriteDataBurst
    oled_display.o(.text.OLED_FrameBuffer_Update) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    oled_display.o(.text.OLED_FrameBuffer_Update) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update) refers to oled_display.o(.text.OLED_FrameBuffer_Update) for [Anonymous Symbol]
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_SetDisplayWindow) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_SetDisplayWindow) refers to oled_display.o(.text.OLED_WriteCmd) for OLED_WriteCmd
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow) refers to oled_display.o(.text.OLED_SetDisplayWindow) for [Anonymous Symbol]
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_WriteDataBurst) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_WriteDataBurst) refers to oled_display.o(.text.I2C_Start) for I2C_Start
    oled_display.o(.text.OLED_WriteDataBurst) refers to oled_display.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled_display.o(.text.OLED_WriteDataBurst) refers to oled_display.o(.text.I2C_WriteBytes) for I2C_WriteBytes
    oled_display.o(.text.OLED_WriteDataBurst) refers to oled_display.o(.text.I2C_Stop) for I2C_Stop
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst) refers to oled_display.o(.text.OLED_WriteDataBurst) for [Anonymous Symbol]
    oled_display.o(.text.OLED_Display_Process) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_Display_Process) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_Display_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_Display_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_Display_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_Display_Process) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_Display_Process) refers to oled_display.o(.text.OLED_FrameBuffer_Update) for OLED_FrameBuffer_Update
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_Display_Process) refers to oled_display.o(.text.OLED_Display_Process) for [Anonymous Symbol]
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.I2C_WriteBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.I2C_WriteBytes) refers to oled_display.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.I2C_WriteBytes) refers to oled_display.o(.text.I2C_WriteBytes) for [Anonymous Symbol]
    oled_display.o(.text.OLED_SetPos) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_SetPos) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_SetPos) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_SetPos) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_SetPos) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_SetPos) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_SetPos) refers to oled_display.o(.text.OLED_WriteCmd) for OLED_WriteCmd
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_SetPos) refers to oled_display.o(.text.OLED_SetPos) for [Anonymous Symbol]
    oled_display.o(.text.OLED_Clear) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_Clear) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_Clear) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_Clear) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_Clear) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_Clear) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled_display.o(.text.OLED_Clear) refers to oled_display.o(.bss.g_oled_fb) for g_oled_fb
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_Clear) refers to oled_display.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowChar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowChar) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowChar) refers to oled_display.o(.text.OLED_FrameBuffer_SetPixel) for OLED_FrameBuffer_SetPixel
    oled_display.o(.text.OLED_ShowChar) refers to font.o(.rodata.ASCII_8x16) for ASCII_8x16
    oled_display.o(.text.OLED_ShowChar) refers to font.o(.rodata.ASCII_8x8) for ASCII_8x8
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowChar) refers to oled_display.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowString) refers to strlen.o(.text) for strlen
    oled_display.o(.text.OLED_ShowString) refers to oled_display.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled_display.o(.text.OLED_ShowString) refers to oled_display.o(.text.OLED_ShowChinese) for OLED_ShowChinese
    oled_display.o(.text.OLED_ShowString) refers to font.o(.rodata.Chinese_16x16) for Chinese_16x16
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowString) refers to oled_display.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowChinese) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowChinese) refers to oled_display.o(.text.OLED_FrameBuffer_SetPixel) for OLED_FrameBuffer_SetPixel
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowChinese) refers to oled_display.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowChineseByIndex) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowChineseByIndex) refers to oled_display.o(.text.OLED_ShowChinese) for OLED_ShowChinese
    oled_display.o(.text.OLED_ShowChineseByIndex) refers to font.o(.rodata.Chinese_16x16) for Chinese_16x16
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex) refers to oled_display.o(.text.OLED_ShowChineseByIndex) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowFloat) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowFloat) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_display.o(.text.OLED_ShowFloat) refers to noretval__2sprintf.o(.text) for __2sprintf
    oled_display.o(.text.OLED_ShowFloat) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowFloat) refers to oled_display.o(.text.OLED_ShowFloat) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowNumber) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowNumber) refers to noretval__2sprintf.o(.text) for __2sprintf
    oled_display.o(.text.OLED_ShowNumber) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowNumber) refers to oled_display.o(.text.OLED_ShowNumber) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowSmallString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowSmallString) refers to oled_display.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowSmallString) refers to oled_display.o(.text.OLED_ShowSmallString) for [Anonymous Symbol]
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.text.OLED_ShowLargeString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.text.OLED_ShowLargeString) refers to oled_display.o(.text.OLED_ShowString) for OLED_ShowString
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_display.o(.ARM.exidx.text.OLED_ShowLargeString) refers to oled_display.o(.text.OLED_ShowLargeString) for [Anonymous Symbol]
    oled_display.o(.bss.g_oled_fb) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled_display.o(.bss.g_oled_fb) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled_display.o(.bss.g_oled_fb) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    oled_display.o(.bss.g_oled_fb) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_display.o(.bss.g_oled_fb) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    oled_display.o(.bss.g_oled_fb) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    system_monitor.o(.text.System_Monitor_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    system_monitor.o(.text.System_Monitor_Init) refers to system_monitor.o(.bss.g_system_status) for g_system_status
    system_monitor.o(.text.System_Monitor_Init) refers to system_monitor.o(.bss.channel_comm_status) for [Anonymous Symbol]
    system_monitor.o(.ARM.exidx.text.System_Monitor_Init) refers to system_monitor.o(.text.System_Monitor_Init) for [Anonymous Symbol]
    system_monitor.o(.text.System_Monitor_Update) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    system_monitor.o(.text.System_Monitor_Update) refers to system_monitor.o(.bss.g_system_status) for g_system_status
    system_monitor.o(.ARM.exidx.text.System_Monitor_Update) refers to system_monitor.o(.text.System_Monitor_Update) for [Anonymous Symbol]
    system_monitor.o(.text.System_Check_Communication) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    system_monitor.o(.text.System_Check_Communication) refers to system_monitor.o(.bss.g_system_status) for g_system_status
    system_monitor.o(.ARM.exidx.text.System_Check_Communication) refers to system_monitor.o(.text.System_Check_Communication) for [Anonymous Symbol]
    system_monitor.o(.text.System_Get_Communication_Status) refers to system_monitor.o(.bss.channel_comm_status) for [Anonymous Symbol]
    system_monitor.o(.ARM.exidx.text.System_Get_Communication_Status) refers to system_monitor.o(.text.System_Get_Communication_Status) for [Anonymous Symbol]
    system_monitor.o(.text.System_Set_Communication_Status) refers to system_monitor.o(.bss.channel_comm_status) for [Anonymous Symbol]
    system_monitor.o(.ARM.exidx.text.System_Set_Communication_Status) refers to system_monitor.o(.text.System_Set_Communication_Status) for [Anonymous Symbol]
    system_status.o(.text.System_Status_Init) refers to system_status.o(.bss.g_system_state) for g_system_state
    system_status.o(.text.System_Status_Init) refers to system_status.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    system_status.o(.ARM.exidx.text.System_Status_Init) refers to system_status.o(.text.System_Status_Init) for [Anonymous Symbol]
    system_status.o(.text.LED_Control) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    system_status.o(.text.LED_Control) refers to system_status.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    system_status.o(.ARM.exidx.text.LED_Control) refers to system_status.o(.text.LED_Control) for [Anonymous Symbol]
    system_status.o(.text.System_Status_Update) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    system_status.o(.text.System_Status_Update) refers to system_monitor.o(.text.System_Check_Communication) for System_Check_Communication
    system_status.o(.text.System_Status_Update) refers to system_status.o(.text.LED_Control) for LED_Control
    system_status.o(.text.System_Status_Update) refers to system_status.o(.text.LED_Update) for LED_Update
    system_status.o(.text.System_Status_Update) refers to system_status.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    system_status.o(.text.System_Status_Update) refers to system_status.o(.bss.g_system_state) for g_system_state
    system_status.o(.ARM.exidx.text.System_Status_Update) refers to system_status.o(.text.System_Status_Update) for [Anonymous Symbol]
    system_status.o(.text.LED_Update) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    system_status.o(.text.LED_Update) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    system_status.o(.text.LED_Update) refers to system_status.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    system_status.o(.ARM.exidx.text.LED_Update) refers to system_status.o(.text.LED_Update) for [Anonymous Symbol]
    system_status.o(.ARM.exidx.text.Watchdog_Refresh) refers to system_status.o(.text.Watchdog_Refresh) for [Anonymous Symbol]
    system_status.o(.ARM.exidx.text.Watchdog_Init) refers to system_status.o(.text.Watchdog_Init) for [Anonymous Symbol]
    modbus_extended.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Modbus_Extended_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Modbus_Extended_Init) refers to modbus_extended.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_extended.o(.text.Modbus_Extended_Init) refers to config_manager.o(.bss.g_config) for g_config
    modbus_extended.o(.text.Modbus_Extended_Init) refers to modbus_extended.o(.bss.g_sf6_data) for g_sf6_data
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init) refers to modbus_extended.o(.text.Modbus_Extended_Init) for [Anonymous Symbol]
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Get_Baudrate_Code) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code) refers to modbus_extended.o(.text.Get_Baudrate_Code) for [Anonymous Symbol]
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Modbus_Extended_Process) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Modbus_Extended_Process) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_extended.o(.text.Modbus_Extended_Process) refers to modbus_extended.o(.text.Process_Extended_Frame) for Process_Extended_Frame
    modbus_extended.o(.text.Modbus_Extended_Process) refers to uart_dma.o(.text.UART1_Send_Data) for UART1_Send_Data
    modbus_extended.o(.text.Modbus_Extended_Process) refers to config_manager.o(.bss.g_config) for g_config
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process) refers to modbus_extended.o(.text.Modbus_Extended_Process) for [Anonymous Symbol]
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Process_Extended_Frame) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Process_Extended_Frame) refers to modbus_extended.o(.text.Process_Read_Object) for Process_Read_Object
    modbus_extended.o(.text.Process_Extended_Frame) refers to modbus_extended.o(.text.Process_Read_All_Objects) for Process_Read_All_Objects
    modbus_extended.o(.text.Process_Extended_Frame) refers to modbus_extended.o(.text.Process_Write_Object) for Process_Write_Object
    modbus_extended.o(.text.Process_Extended_Frame) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    modbus_extended.o(.text.Process_Extended_Frame) refers to modbus_master.o(.text.Modbus_CRC16) for Modbus_CRC16
    modbus_extended.o(.text.Process_Extended_Frame) refers to modbus_extended.o(.bss.g_ext_debug) for g_ext_debug
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame) refers to modbus_extended.o(.text.Process_Extended_Frame) for [Anonymous Symbol]
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Process_Read_All_Objects) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Process_Read_All_Objects) refers to modbus_extended.o(.text.Process_Read_Object) for Process_Read_Object
    modbus_extended.o(.text.Process_Read_All_Objects) refers to modbus_extended.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects) refers to modbus_extended.o(.text.Process_Read_All_Objects) for [Anonymous Symbol]
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Process_Read_Object) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Process_Read_Object) refers to modbus_extended.o(.text.TLV_Encode_DateTime) for TLV_Encode_DateTime
    modbus_extended.o(.text.Process_Read_Object) refers to strlen.o(.text) for strlen
    modbus_extended.o(.text.Process_Read_Object) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    modbus_extended.o(.text.Process_Read_Object) refers to modbus_extended.o(.bss.g_sf6_data) for g_sf6_data
    modbus_extended.o(.text.Process_Read_Object) refers to modbus_extended.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Process_Read_Object) refers to modbus_extended.o(.text.Process_Read_Object) for [Anonymous Symbol]
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Process_Write_Object) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Process_Write_Object) refers to config_manager.o(.text.Set_SF6_Threshold) for Set_SF6_Threshold
    modbus_extended.o(.text.Process_Write_Object) refers to config_manager.o(.text.Set_Slave_Address) for Set_Slave_Address
    modbus_extended.o(.text.Process_Write_Object) refers to config_manager.o(.text.Set_Slave_Baudrate) for Set_Slave_Baudrate
    modbus_extended.o(.text.Process_Write_Object) refers to modbus_extended.o(.bss.g_sf6_data) for g_sf6_data
    modbus_extended.o(.text.Process_Write_Object) refers to modbus_extended.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    modbus_extended.o(.text.Process_Write_Object) refers to modbus_extended.o(.rodata.cst16) for .Lswitch.table
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Process_Write_Object) refers to modbus_extended.o(.text.Process_Write_Object) for [Anonymous Symbol]
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to modbus_extended.o(.rodata.str1.4) for .L.str.1
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to strlen.o(.text) for strlen
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to modbus_extended.o(.bss.g_ext_debug) for g_ext_debug
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to modbus_extended.o(.rodata.str1.1) for .L.str.2
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to noretval__2sprintf.o(.text) for __2sprintf
    modbus_extended.o(.text.Debug_Print_Extended_Info) refers to config_manager.o(.bss.g_config) for g_config
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info) refers to modbus_extended.o(.text.Debug_Print_Extended_Info) for [Anonymous Symbol]
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Get_Extended_Debug_Info) refers to modbus_extended.o(.bss.g_ext_debug) for g_ext_debug
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info) refers to modbus_extended.o(.text.Get_Extended_Debug_Info) for [Anonymous Symbol]
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Get_Baudrate_Value) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Get_Baudrate_Value) refers to modbus_extended.o(.rodata.cst16) for .Lswitch.table
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value) refers to modbus_extended.o(.text.Get_Baudrate_Value) for [Anonymous Symbol]
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Validate_Object_Identifier) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier) refers to modbus_extended.o(.text.Validate_Object_Identifier) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Encode_Boolean) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean) refers to modbus_extended.o(.text.TLV_Encode_Boolean) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Encode_UTiny) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny) refers to modbus_extended.o(.text.TLV_Encode_UTiny) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Encode_Float) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float) refers to modbus_extended.o(.text.TLV_Encode_Float) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Encode_DateTime) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime) refers to modbus_extended.o(.text.TLV_Encode_DateTime) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Encode_OctetString) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.TLV_Encode_OctetString) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString) refers to modbus_extended.o(.text.TLV_Encode_OctetString) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Decode_Boolean) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean) refers to modbus_extended.o(.text.TLV_Decode_Boolean) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Decode_UTiny) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny) refers to modbus_extended.o(.text.TLV_Decode_UTiny) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Decode_Float) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float) refers to modbus_extended.o(.text.TLV_Decode_Float) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Decode_DateTime) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime) refers to modbus_extended.o(.text.TLV_Decode_DateTime) for [Anonymous Symbol]
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.TLV_Decode_OctetString) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.TLV_Decode_OctetString) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString) refers to modbus_extended.o(.text.TLV_Decode_OctetString) for [Anonymous Symbol]
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpeq
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers to config_manager.o(.bss.corrected_data) for corrected_data
    modbus_extended.o(.text.Update_SF6_Data_From_Modbus) refers to modbus_extended.o(.bss.g_sf6_data) for g_sf6_data
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus) refers to modbus_extended.o(.text.Update_SF6_Data_From_Modbus) for [Anonymous Symbol]
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.bss.g_sf6_data) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.bss.g_ext_debug) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.rodata.str1.4) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.rodata.str1.1) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.rodata.cst16) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_hex_int.o(.text) for _printf_longlong_hex
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_pad.o(.text) for _printf_pre_padding
    modbus_extended.o(.bss..L_MergedGlobals) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    uart_dma.o(.text.UART_DMA_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_dma.o(.text.UART_DMA_Init) refers to uart_dma.o(.text.UART1_Start_Receive) for UART1_Start_Receive
    uart_dma.o(.text.UART_DMA_Init) refers to uart_dma.o(.text.UART2_Start_Receive) for UART2_Start_Receive
    uart_dma.o(.text.UART_DMA_Init) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.text.UART_DMA_Init) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.UART_DMA_Init) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.text.UART_DMA_Init) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.ARM.exidx.text.UART_DMA_Init) refers to uart_dma.o(.text.UART_DMA_Init) for [Anonymous Symbol]
    uart_dma.o(.text.UART1_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_dma.o(.text.UART1_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    uart_dma.o(.text.UART1_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) for HAL_UART_DeInit
    uart_dma.o(.text.UART1_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    uart_dma.o(.text.UART1_Start_Receive) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.text.UART1_Start_Receive) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.ARM.exidx.text.UART1_Start_Receive) refers to uart_dma.o(.text.UART1_Start_Receive) for [Anonymous Symbol]
    uart_dma.o(.text.UART2_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_dma.o(.text.UART2_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    uart_dma.o(.text.UART2_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) for HAL_UART_DeInit
    uart_dma.o(.text.UART2_Start_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    uart_dma.o(.text.UART2_Start_Receive) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.UART2_Start_Receive) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.ARM.exidx.text.UART2_Start_Receive) refers to uart_dma.o(.text.UART2_Start_Receive) for [Anonymous Symbol]
    uart_dma.o(.text.UART1_Send_Data) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    uart_dma.o(.text.UART1_Send_Data) refers to modbus_slave.o(.text.RS485_Slave_Set_Mode) for RS485_Slave_Set_Mode
    uart_dma.o(.text.UART1_Send_Data) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    uart_dma.o(.text.UART1_Send_Data) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.text.UART1_Send_Data) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.ARM.exidx.text.UART1_Send_Data) refers to uart_dma.o(.text.UART1_Send_Data) for [Anonymous Symbol]
    uart_dma.o(.text.UART2_Send_Data) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    uart_dma.o(.text.UART2_Send_Data) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    uart_dma.o(.text.UART2_Send_Data) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.UART2_Send_Data) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.ARM.exidx.text.UART2_Send_Data) refers to uart_dma.o(.text.UART2_Send_Data) for [Anonymous Symbol]
    uart_dma.o(.text.UART_DMA_Process) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.text.UART1_Frame_Received_Callback) for UART1_Frame_Received_Callback
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.text.UART1_Start_Receive) for UART1_Start_Receive
    uart_dma.o(.text.UART_DMA_Process) refers to modbus_master.o(.text.Modbus_Master_Process_Response) for Modbus_Master_Process_Response
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.text.UART2_Start_Receive) for UART2_Start_Receive
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.UART_DMA_Process) refers to uart_dma.o(.bss.UART_DMA_Process.last_check_time) for [Anonymous Symbol]
    uart_dma.o(.text.UART_DMA_Process) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.text.UART_DMA_Process) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.ARM.exidx.text.UART_DMA_Process) refers to uart_dma.o(.text.UART_DMA_Process) for [Anonymous Symbol]
    uart_dma.o(.text.UART1_Frame_Received_Callback) refers to uart_dma.o(.text.Split_Modbus_Frames) for Split_Modbus_Frames
    uart_dma.o(.ARM.exidx.text.UART1_Frame_Received_Callback) refers to uart_dma.o(.text.UART1_Frame_Received_Callback) for [Anonymous Symbol]
    uart_dma.o(.text.UART2_Frame_Received_Callback) refers to modbus_master.o(.text.Modbus_Master_Process_Response) for Modbus_Master_Process_Response
    uart_dma.o(.ARM.exidx.text.UART2_Frame_Received_Callback) refers to uart_dma.o(.text.UART2_Frame_Received_Callback) for [Anonymous Symbol]
    uart_dma.o(.text.Split_Modbus_Frames) refers to modbus_extended.o(.text.Modbus_Extended_Process) for Modbus_Extended_Process
    uart_dma.o(.text.Split_Modbus_Frames) refers to modbus_slave.o(.text.Modbus_Slave_Process_Frame) for Modbus_Slave_Process_Frame
    uart_dma.o(.ARM.exidx.text.Split_Modbus_Frames) refers to uart_dma.o(.text.Split_Modbus_Frames) for [Anonymous Symbol]
    uart_dma.o(.text.UART1_TX_Complete_Callback) refers to modbus_slave.o(.text.RS485_Slave_Set_Mode) for RS485_Slave_Set_Mode
    uart_dma.o(.ARM.exidx.text.UART1_TX_Complete_Callback) refers to uart_dma.o(.text.UART1_TX_Complete_Callback) for [Anonymous Symbol]
    uart_dma.o(.text.UART2_TX_Complete_Callback) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.ARM.exidx.text.UART2_TX_Complete_Callback) refers to uart_dma.o(.text.UART2_TX_Complete_Callback) for [Anonymous Symbol]
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to uart_dma.o(.text.UART1_Start_Receive) for UART1_Start_Receive
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to uart_dma.o(.text.UART2_Start_Receive) for UART2_Start_Receive
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.text.HAL_UART_IdleCallback) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.ARM.exidx.text.HAL_UART_IdleCallback) refers to uart_dma.o(.text.HAL_UART_IdleCallback) for [Anonymous Symbol]
    uart_dma.o(.text.HAL_UART_TxCpltCallback) refers to modbus_slave.o(.text.RS485_Slave_Set_Mode) for RS485_Slave_Set_Mode
    uart_dma.o(.text.HAL_UART_TxCpltCallback) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to uart_dma.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to uart_dma.o(.text.UART1_Start_Receive) for UART1_Start_Receive
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to uart_dma.o(.text.UART2_Start_Receive) for UART2_Start_Receive
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to main.o(.bss..L_MergedGlobals.1) for huart2
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to uart_dma.o(.bss.g_uart2_data) for g_uart2_data
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to main.o(.bss..L_MergedGlobals) for huart1
    uart_dma.o(.text.HAL_UART_ErrorCallback) refers to uart_dma.o(.bss.g_uart1_data) for g_uart1_data
    uart_dma.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to uart_dma.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_ConfigEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_ConfigEventout) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_EnableEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_EnableEventout) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_DisableEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_DisableEventout) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Init) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Init) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_ConversionStop_Disable) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to stm32f1xx_hal_msp.o(.text.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Start) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.ADC_Enable) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(.text.ADC_Enable) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_Enable) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForConversion) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForEvent) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForEvent) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_IT) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_IT) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAConvCplt) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.ADC_DMAError) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAError) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_GetValue) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f1xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError) refers to stm32f1xx_hal_adc.o(.text.HAL_ADC_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel) refers to stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f1xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f1xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_DeInit) refers to stm32f1xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f1xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f1xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_IncTick) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f1xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTick) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_Delay) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(.text.HAL_Delay) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f1xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f1xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f1xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f1xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f1xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f1xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f1xx_1.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx_1.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx_1.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.text.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig) refers to stm32f1xx_hal_dma.o(.text.DMA_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.PWR_OverloadWfe) refers to stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f1xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) refers to stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to uart_dma.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAError) refers to stm32f1xx_hal_uart.o(.text.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(.text.UART_DMAError) refers to stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(.text.UART_DMAError) refers to uart_dma.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(.text.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_EndTxTransfer) refers to stm32f1xx_hal_uart.o(.text.UART_EndTxTransfer) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_EndRxTransfer) refers to stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_dma.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_dma.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_Receive_IT) refers to modbus_slave.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to uart_dma.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f1xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to modbus_slave.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f1xx_1.o(.ARM.exidx.text.SystemInit) refers to system_stm32f1xx_1.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f1xx_1.o(.text.SystemCoreClockUpdate) refers to system_stm32f1xx_1.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f1xx_1.o(.text.SystemCoreClockUpdate) refers to system_stm32f1xx_1.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f1xx_1.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f1xx_1.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f1xx_it.o(.text), (0 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel4_IRQHandler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel5_IRQHandler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel6_IRQHandler), (8 bytes).
    Removing stm32f1xx_it.o(.ARM.exidx.text.DMA1_Channel7_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f1xx_hal_msp.o(.text.HAL_ADC_MspDeInit), (40 bytes).
    Removing stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f1xx_hal_msp.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing config_manager.o(.text), (0 bytes).
    Removing config_manager.o(.ARM.exidx.text.Config_Init), (8 bytes).
    Removing config_manager.o(.text.Config_Load), (32 bytes).
    Removing config_manager.o(.ARM.exidx.text.Config_Load), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Config_Save), (8 bytes).
    Removing config_manager.o(.text.Config_Validate), (148 bytes).
    Removing config_manager.o(.ARM.exidx.text.Config_Validate), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Validate_Slave_Address), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Set_Slave_Address), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Set_Slave_Baudrate), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Set_SF6_Threshold), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Set_Pressure_Correction), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Set_Moisture_Correction), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Apply_Data_Corrections), (8 bytes).
    Removing config_manager.o(.ARM.exidx.text.Get_Corrected_Register_Value), (8 bytes).
    Removing font.o(.text), (0 bytes).
    Removing key_handler.o(.text), (0 bytes).
    Removing key_handler.o(.ARM.exidx.text.Key_Scan_In_ISR), (8 bytes).
    Removing key_handler.o(.text.Key_Scan), (40 bytes).
    Removing key_handler.o(.ARM.exidx.text.Key_Scan), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Key_Init), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Reset_Setting_Variables), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Key_Process), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Process_Password_Menu), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Process_Slave_Setting_Menu), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Process_Master_Setting_Menu), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Process_Advanced_Setting_Menu), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Update_Menu_Display), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Display_Startup_Screen), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Display_Data_Screen), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Display_Address_Screen), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Display_Password_Screen), (8 bytes).
    Removing key_handler.o(.ARM.exidx.text.Display_Setting_Screen), (8 bytes).
    Removing modbus_master.o(.text), (0 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Modbus_Master_Init), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.RS485_Set_Mode), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Modbus_Master_Process), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Modbus_CRC16), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Validate_Response), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Parse_Response_Data), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Handle_Communication_Error), (8 bytes).
    Removing modbus_master.o(.ARM.exidx.text.Modbus_Master_Process_Response), (8 bytes).
    Removing modbus_slave.o(.text), (0 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Init), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.RS485_Slave_Set_Mode), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Process), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Modbus_Slave_Process_Frame), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Process_Modbus_Frame), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Process_Read_Coils), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Process_Read_Holding_Registers), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Process_Write_Single_Register), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.Read_Config_Register), (8 bytes).
    Removing modbus_slave.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing oled_display.o(.text), (0 bytes).
    Removing oled_display.o(.ARM.exidx.text.HAL_Delay_us), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_WriteCmd), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.I2C_Start), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.I2C_WriteByte), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.I2C_Stop), (8 bytes).
    Removing oled_display.o(.text.OLED_WriteData), (36 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Init), (8 bytes).
    Removing oled_display.o(.text.OLED_FrameBuffer_Clear), (32 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Clear), (8 bytes).
    Removing oled_display.o(.text.OLED_FrameBuffer_ClearArea), (176 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ClearArea), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_SetPixel), (8 bytes).
    Removing oled_display.o(.text.OLED_FrameBuffer_GetPixel), (48 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_GetPixel), (8 bytes).
    Removing oled_display.o(.text.OLED_FrameBuffer_MarkDirty), (44 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_MarkDirty), (8 bytes).
    Removing oled_display.o(.text.OLED_FrameBuffer_ForceRefresh), (28 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_ForceRefresh), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_FrameBuffer_Update), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_SetDisplayWindow), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_WriteDataBurst), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_Display_Process), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.I2C_WriteBytes), (8 bytes).
    Removing oled_display.o(.text.OLED_SetPos), (44 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_SetPos), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing oled_display.o(.text.OLED_ShowChineseByIndex), (60 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowChineseByIndex), (8 bytes).
    Removing oled_display.o(.text.OLED_ShowFloat), (56 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowFloat), (8 bytes).
    Removing oled_display.o(.text.OLED_ShowNumber), (44 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowNumber), (8 bytes).
    Removing oled_display.o(.text.OLED_ShowSmallString), (48 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowSmallString), (8 bytes).
    Removing oled_display.o(.text.OLED_ShowLargeString), (6 bytes).
    Removing oled_display.o(.ARM.exidx.text.OLED_ShowLargeString), (8 bytes).
    Removing system_monitor.o(.text), (0 bytes).
    Removing system_monitor.o(.ARM.exidx.text.System_Monitor_Init), (8 bytes).
    Removing system_monitor.o(.ARM.exidx.text.System_Monitor_Update), (8 bytes).
    Removing system_monitor.o(.ARM.exidx.text.System_Check_Communication), (8 bytes).
    Removing system_monitor.o(.ARM.exidx.text.System_Get_Communication_Status), (8 bytes).
    Removing system_monitor.o(.ARM.exidx.text.System_Set_Communication_Status), (8 bytes).
    Removing system_status.o(.text), (0 bytes).
    Removing system_status.o(.ARM.exidx.text.System_Status_Init), (8 bytes).
    Removing system_status.o(.ARM.exidx.text.LED_Control), (8 bytes).
    Removing system_status.o(.ARM.exidx.text.System_Status_Update), (8 bytes).
    Removing system_status.o(.ARM.exidx.text.LED_Update), (8 bytes).
    Removing system_status.o(.text.Watchdog_Refresh), (2 bytes).
    Removing system_status.o(.ARM.exidx.text.Watchdog_Refresh), (8 bytes).
    Removing system_status.o(.ARM.exidx.text.Watchdog_Init), (8 bytes).
    Removing modbus_extended.o(.text), (0 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Init), (8 bytes).
    Removing modbus_extended.o(.text.Get_Baudrate_Code), (42 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Code), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Modbus_Extended_Process), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Process_Extended_Frame), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Process_Read_All_Objects), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Process_Read_Object), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Process_Write_Object), (8 bytes).
    Removing modbus_extended.o(.text.Debug_Print_Extended_Info), (528 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Debug_Print_Extended_Info), (8 bytes).
    Removing modbus_extended.o(.text.Get_Extended_Debug_Info), (84 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Get_Extended_Debug_Info), (8 bytes).
    Removing modbus_extended.o(.text.Get_Baudrate_Value), (24 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Get_Baudrate_Value), (8 bytes).
    Removing modbus_extended.o(.text.Validate_Object_Identifier), (40 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Validate_Object_Identifier), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Encode_Boolean), (18 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Encode_Boolean), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Encode_UTiny), (14 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Encode_UTiny), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Encode_Float), (16 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Encode_Float), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Encode_DateTime), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Encode_OctetString), (24 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Encode_OctetString), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Decode_Boolean), (32 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Decode_Boolean), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Decode_UTiny), (26 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Decode_UTiny), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Decode_Float), (28 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Decode_Float), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Decode_DateTime), (52 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Decode_DateTime), (8 bytes).
    Removing modbus_extended.o(.text.TLV_Decode_OctetString), (36 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.TLV_Decode_OctetString), (8 bytes).
    Removing modbus_extended.o(.ARM.exidx.text.Update_SF6_Data_From_Modbus), (8 bytes).
    Removing modbus_extended.o(.rodata.str1.4), (36 bytes).
    Removing modbus_extended.o(.rodata.str1.1), (142 bytes).
    Removing uart_dma.o(.text), (0 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART_DMA_Init), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART1_Start_Receive), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART2_Start_Receive), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART1_Send_Data), (8 bytes).
    Removing uart_dma.o(.text.UART2_Send_Data), (68 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART2_Send_Data), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART_DMA_Process), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART1_Frame_Received_Callback), (8 bytes).
    Removing uart_dma.o(.text.UART2_Frame_Received_Callback), (4 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART2_Frame_Received_Callback), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.Split_Modbus_Frames), (8 bytes).
    Removing uart_dma.o(.text.UART1_TX_Complete_Callback), (6 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART1_TX_Complete_Callback), (8 bytes).
    Removing uart_dma.o(.text.UART2_TX_Complete_Callback), (16 bytes).
    Removing uart_dma.o(.ARM.exidx.text.UART2_TX_Complete_Callback), (8 bytes).
    Removing uart_dma.o(.text.HAL_UART_IdleCallback), (172 bytes).
    Removing uart_dma.o(.ARM.exidx.text.HAL_UART_IdleCallback), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing uart_dma.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_ConfigEventout), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_EnableEventout), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_DisableEventout), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text), (0 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_ConversionStop_Disable), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_DeInit), (204 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Start), (212 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.ADC_Enable), (128 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_Enable), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop), (52 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForConversion), (340 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_PollForEvent), (96 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_IT), (220 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_IT), (62 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Start_DMA), (280 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.ADC_DMAConvCplt), (80 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.ADC_DMAHalfConvCplt), (6 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.ADC_DMAError), (22 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_Stop_DMA), (90 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_IRQHandler), (248 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig), (88 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_GetState), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState), (8 bytes).
    Removing stm32f1xx_hal_adc.o(.text.HAL_ADC_GetError), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start), (220 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart), (172 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop), (82 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion), (356 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT), (180 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT), (92 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA), (224 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA), (124 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue), (34 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue), (32 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel), (460 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel), (88 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel), (8 bytes).
    Removing stm32f1xx_hal.o(.text), (0 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_SetTickFreq), (32 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_SuspendTick), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_ResumeTick), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (76 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (108 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (36 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (204 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (40 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (20 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_DeInit), (84 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Start), (82 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (556 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (78 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (86 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (120 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (72 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.PWR_OverloadWfe), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (84 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (296 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase), (92 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig), (104 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (520 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (128 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (102 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_LIN_Init), (118 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (126 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (48 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DMAResume), (128 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_EndTxTransfer), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_EndRxTransfer), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (268 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Abort), (214 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (102 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback), (40 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback), (40 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (42 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f1xx_1.o(.text), (0 bytes).
    Removing system_stm32f1xx_1.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f1xx_1.o(.text.SystemCoreClockUpdate), (96 bytes).
    Removing system_stm32f1xx_1.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).

612 unused section(s) (total 16902 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/config_manager.c             0x00000000   Number         0  config_manager.o ABSOLUTE
    ../Core/Src/font.c                       0x00000000   Number         0  font.o ABSOLUTE
    ../Core/Src/key_handler.c                0x00000000   Number         0  key_handler.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/modbus_extended.c            0x00000000   Number         0  modbus_extended.o ABSOLUTE
    ../Core/Src/modbus_master.c              0x00000000   Number         0  modbus_master.o ABSOLUTE
    ../Core/Src/modbus_slave.c               0x00000000   Number         0  modbus_slave.o ABSOLUTE
    ../Core/Src/oled_display.c               0x00000000   Number         0  oled_display.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_monitor.c             0x00000000   Number         0  system_monitor.o ABSOLUTE
    ../Core/Src/system_status.c              0x00000000   Number         0  system_status.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx_1.o ABSOLUTE
    ../Core/Src/uart_dma.c                   0x00000000   Number         0  uart_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmp.s                          0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fgeqf.s                         0x00000000   Number         0  fgeqf.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000160   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000166   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800016c   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000172   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x08000178   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800017e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000182   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000184   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800018a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000194   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000194   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000196   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000198   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000198   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800019a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800019a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800019a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001a0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001a0   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001a4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001a4   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ac   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ae   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ae   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001b2   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001b8   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x080001f8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000220   Section        0  _printf_pad.o(.text)
    .text                                    0x0800026e   Section        0  _printf_str.o(.text)
    .text                                    0x080002c0   Section        0  _printf_dec.o(.text)
    .text                                    0x08000338   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000390   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000518   Section        0  strlen.o(.text)
    .text                                    0x08000556   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080005e0   Section       68  rt_memclr.o(.text)
    .text                                    0x08000624   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000672   Section        0  heapauxi.o(.text)
    .text                                    0x08000678   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800072a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800072d   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000b48   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000b49   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b78   Section        0  _sputc.o(.text)
    .text                                    0x08000b82   Section        0  _printf_char.o(.text)
    .text                                    0x08000bae   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000c14   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000c1c   Section      138  lludiv10.o(.text)
    .text                                    0x08000ca8   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000d28   Section        0  bigflt0.o(.text)
    .text                                    0x08000e0c   Section        8  libspace.o(.text)
    .text                                    0x08000e14   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000e5e   Section        0  exit.o(.text)
    .text                                    0x08000e70   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000ef0   Section        0  sys_exit.o(.text)
    .text                                    0x08000efc   Section        2  use_no_semi.o(.text)
    [Anonymous Symbol]                       0x08000efe   Section        0  stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable)
    .text                                    0x08000efe   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x08000f50   Section        0  config_manager.o(.text.Apply_Data_Corrections)
    __arm_cp.10_2                            0x08000fc8   Number         4  config_manager.o(.text.Apply_Data_Corrections)
    [Anonymous Symbol]                       0x08000fcc   Section        0  stm32f1xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000fd0   Section        0  config_manager.o(.text.Config_Init)
    __arm_cp.0_2                             0x08001078   Number         4  config_manager.o(.text.Config_Init)
    __arm_cp.0_3                             0x0800107c   Number         4  config_manager.o(.text.Config_Init)
    __arm_cp.0_4                             0x08001080   Number         4  config_manager.o(.text.Config_Init)
    [Anonymous Symbol]                       0x08001084   Section        0  config_manager.o(.text.Config_Save)
    __arm_cp.2_0                             0x080010c8   Number         4  config_manager.o(.text.Config_Save)
    [Anonymous Symbol]                       0x080010cc   Section        0  stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler)
    __arm_cp.11_0                            0x080010d4   Number         4  stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler)
    [Anonymous Symbol]                       0x080010d8   Section        0  stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler)
    __arm_cp.12_0                            0x080010e0   Number         4  stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler)
    [Anonymous Symbol]                       0x080010e4   Section        0  stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler)
    __arm_cp.13_0                            0x080010ec   Number         4  stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler)
    [Anonymous Symbol]                       0x080010f0   Section        0  stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler)
    __arm_cp.14_0                            0x080010f8   Number         4  stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler)
    [Anonymous Symbol]                       0x080010fc   Section        0  stm32f1xx_hal_dma.o(.text.DMA_SetConfig)
    DMA_SetConfig                            0x080010fd   Thumb Code    40  stm32f1xx_hal_dma.o(.text.DMA_SetConfig)
    [Anonymous Symbol]                       0x08001124   Section        0  stm32f1xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08001128   Section        0  key_handler.o(.text.Display_Address_Screen)
    __arm_cp.12_0                            0x08001224   Number         4  key_handler.o(.text.Display_Address_Screen)
    __arm_cp.12_2                            0x08001238   Number         4  key_handler.o(.text.Display_Address_Screen)
    __arm_cp.12_4                            0x08001248   Number         4  key_handler.o(.text.Display_Address_Screen)
    __arm_cp.12_6                            0x08001250   Number         4  key_handler.o(.text.Display_Address_Screen)
    [Anonymous Symbol]                       0x08001268   Section        0  key_handler.o(.text.Display_Data_Screen)
    __arm_cp.11_0                            0x08001374   Number         4  key_handler.o(.text.Display_Data_Screen)
    __arm_cp.11_3                            0x08001398   Number         4  key_handler.o(.text.Display_Data_Screen)
    __arm_cp.11_4                            0x0800139c   Number         4  key_handler.o(.text.Display_Data_Screen)
    __arm_cp.11_5                            0x080013a0   Number         4  key_handler.o(.text.Display_Data_Screen)
    __arm_cp.11_6                            0x080013a4   Number         4  key_handler.o(.text.Display_Data_Screen)
    [Anonymous Symbol]                       0x080013c4   Section        0  key_handler.o(.text.Display_Password_Screen)
    __arm_cp.13_1                            0x080013fc   Number         4  key_handler.o(.text.Display_Password_Screen)
    [Anonymous Symbol]                       0x08001404   Section        0  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_0                            0x080015a4   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_1                            0x080015a8   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_8                            0x0800161c   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_12                           0x0800163c   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_13                           0x08001640   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_16                           0x08001660   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_17                           0x08001664   Number         4  key_handler.o(.text.Display_Setting_Screen)
    __arm_cp.14_18                           0x08001668   Number         4  key_handler.o(.text.Display_Setting_Screen)
    [Anonymous Symbol]                       0x0800166c   Section        0  key_handler.o(.text.Display_Startup_Screen)
    __arm_cp.10_1                            0x080016a4   Number         4  key_handler.o(.text.Display_Startup_Screen)
    [Anonymous Symbol]                       0x080016c8   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080016d0   Section        0  stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x080016d1   Thumb Code    88  stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode)
    __arm_cp.4_1                             0x08001720   Number         4  stm32f1xx_hal_flash.o(.text.FLASH_SetErrorCode)
    [Anonymous Symbol]                       0x08001724   Section        0  stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation)
    __arm_cp.1_0                             0x08001780   Number         4  stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation)
    [Anonymous Symbol]                       0x08001784   Section        0  config_manager.o(.text.Get_Corrected_Register_Value)
    __arm_cp.11_0                            0x080017a0   Number         4  config_manager.o(.text.Get_Corrected_Register_Value)
    [Anonymous Symbol]                       0x080017a4   Section        0  stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.23_0                            0x080018b0   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.23_1                            0x080018b4   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    __arm_cp.23_2                            0x080018b8   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    [Anonymous Symbol]                       0x080018bc   Section        0  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_0                             0x080019bc   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_1                             0x080019c0   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_2                             0x080019c4   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    __arm_cp.0_3                             0x080019c8   Number         4  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    [Anonymous Symbol]                       0x080019cc   Section        0  stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit)
    __arm_cp.1_0                             0x08001a20   Number         4  stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit)
    __arm_cp.1_1                             0x08001a24   Number         4  stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit)
    [Anonymous Symbol]                       0x08001a28   Section        0  stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08001a68   Section        0  stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08001af4   Section        0  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_0                             0x08001c1c   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_1                             0x08001c20   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_2                             0x08001c24   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_3                             0x08001c28   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_4                             0x08001c2c   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    __arm_cp.8_5                             0x08001c30   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08001c34   Section        0  stm32f1xx_hal_dma.o(.text.HAL_DMA_Init)
    __arm_cp.0_0                             0x08001ca0   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_Init)
    __arm_cp.0_1                             0x08001ca4   Number         4  stm32f1xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08001ca8   Section        0  stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x08001d18   Section        0  stm32f1xx_hal.o(.text.HAL_Delay)
    __arm_cp.10_0                            0x08001d40   Number         4  stm32f1xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08001d44   Section        0  oled_display.o(.text.HAL_Delay_us)
    __arm_cp.0_0                             0x08001d60   Number         4  oled_display.o(.text.HAL_Delay_us)
    __arm_cp.0_1                             0x08001d64   Number         4  oled_display.o(.text.HAL_Delay_us)
    __arm_cp.0_2                             0x08001d68   Number         4  oled_display.o(.text.HAL_Delay_us)
    [Anonymous Symbol]                       0x08001d6c   Section        0  stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase)
    [Anonymous Symbol]                       0x08001e24   Section        0  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock)
    [Anonymous Symbol]                       0x08001e34   Section        0  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program)
    __arm_cp.0_0                             0x08001edc   Number         4  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program)
    __arm_cp.0_1                             0x08001ee0   Number         4  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program)
    [Anonymous Symbol]                       0x08001ee4   Section        0  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock)
    __arm_cp.7_0                             0x08001f04   Number         4  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock)
    __arm_cp.7_1                             0x08001f08   Number         4  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock)
    __arm_cp.7_2                             0x08001f0c   Number         4  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock)
    [Anonymous Symbol]                       0x08001f10   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit)
    [Anonymous Symbol]                       0x08001fe8   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_0                             0x08002174   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_1                             0x08002178   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_2                             0x0800217c   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_3                             0x08002180   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_4                             0x08002184   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_5                             0x08002188   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_6                             0x0800218c   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_7                             0x08002190   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_9                             0x08002194   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_10                            0x08002198   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_11                            0x0800219c   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_12                            0x080021a0   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    __arm_cp.0_13                            0x080021a4   Number         4  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080021a8   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x080021b2   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x080021bc   Section        0  stm32f1xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x080021c4   Section        0  stm32f1xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080021d0   Section        0  stm32f1xx_hal.o(.text.HAL_Init)
    __arm_cp.0_0                             0x080021f0   Number         4  stm32f1xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x080021f4   Section        0  stm32f1xx_hal.o(.text.HAL_InitTick)
    __arm_cp.1_0                             0x0800222c   Number         4  stm32f1xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08002230   Section        0  stm32f1xx_hal_msp.o(.text.HAL_MspInit)
    __arm_cp.0_0                             0x08002268   Number         4  stm32f1xx_hal_msp.o(.text.HAL_MspInit)
    __arm_cp.0_1                             0x0800226c   Number         4  stm32f1xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08002270   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ)
    __arm_cp.3_0                             0x08002290   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ)
    [Anonymous Symbol]                       0x08002294   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    __arm_cp.2_0                             0x080022ac   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080022b0   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    __arm_cp.1_0                             0x08002304   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08002308   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    __arm_cp.0_0                             0x08002320   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    __arm_cp.0_1                             0x08002324   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08002328   Section        0  stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_1                             0x0800241c   Number         4  stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    __arm_cp.0_2                             0x08002420   Number         4  stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    [Anonymous Symbol]                       0x08002424   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_0                             0x0800254c   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_1                             0x08002550   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    __arm_cp.2_3                             0x08002554   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08002558   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x0800256c   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    __arm_cp.9_0                             0x08002580   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    __arm_cp.9_2                             0x08002584   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08002588   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    __arm_cp.3_0                             0x080025c0   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    __arm_cp.3_1                             0x080025c4   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_GetSysClockFreq.aPLLMULFactorTable 0x080025c8   Number         0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_GetSysClockFreq.aPredivFactorTable 0x080025d8   Number         0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    __arm_cp.3_4                             0x080025dc   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x080025e0   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_0                             0x0800294c   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_1                             0x08002950   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_2                             0x08002954   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_3                             0x08002958   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    __arm_cp.1_4                             0x0800295c   Number         4  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08002960   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.5_0                             0x08002980   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    __arm_cp.5_1                             0x08002984   Number         4  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08002988   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x0800298a   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop)
    [Anonymous Symbol]                       0x080029fe   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit)
    [Anonymous Symbol]                       0x08002a34   Section        0  uart_dma.o(.text.HAL_UART_ErrorCallback)
    __arm_cp.13_2                            0x08002a7c   Number         4  uart_dma.o(.text.HAL_UART_ErrorCallback)
    __arm_cp.13_3                            0x08002a80   Number         4  uart_dma.o(.text.HAL_UART_ErrorCallback)
    __arm_cp.13_4                            0x08002a84   Number         4  uart_dma.o(.text.HAL_UART_ErrorCallback)
    __arm_cp.13_5                            0x08002a88   Number         4  uart_dma.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08002a8c   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    __arm_cp.42_0                            0x08002d2c   Number         4  stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08002d30   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08002d90   Section        0  stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit)
    __arm_cp.4_2                             0x08002dd8   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit)
    __arm_cp.4_3                             0x08002ddc   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit)
    [Anonymous Symbol]                       0x08002de0   Section        0  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_0                             0x08002f68   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_1                             0x08002f6c   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_2                             0x08002f70   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_3                             0x08002f74   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_5                             0x08002f78   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_6                             0x08002f7c   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_7                             0x08002f80   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    __arm_cp.3_8                             0x08002f84   Number         4  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08002f88   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive)
    [Anonymous Symbol]                       0x08003046   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA)
    [Anonymous Symbol]                       0x08003066   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    [Anonymous Symbol]                       0x08003088   Section        0  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    __arm_cp.9_0                             0x080030cc   Number         4  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    __arm_cp.9_1                             0x080030d0   Number         4  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    __arm_cp.9_2                             0x080030d4   Number         4  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    __arm_cp.9_3                             0x080030d8   Number         4  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    __arm_cp.9_4                             0x080030dc   Number         4  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x080030e0   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x080030e2   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08003190   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
    __arm_cp.14_0                            0x080031f8   Number         4  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
    __arm_cp.14_1                            0x080031fc   Number         4  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
    __arm_cp.14_2                            0x08003200   Number         4  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
    [Anonymous Symbol]                       0x08003204   Section        0  uart_dma.o(.text.HAL_UART_TxCpltCallback)
    __arm_cp.12_0                            0x08003224   Number         4  uart_dma.o(.text.HAL_UART_TxCpltCallback)
    __arm_cp.12_1                            0x08003228   Number         4  uart_dma.o(.text.HAL_UART_TxCpltCallback)
    __arm_cp.12_2                            0x0800322c   Number         4  uart_dma.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08003230   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback)
    [Anonymous Symbol]                       0x08003234   Section        0  modbus_master.o(.text.Handle_Communication_Error)
    __arm_cp.6_0                             0x08003274   Number         4  modbus_master.o(.text.Handle_Communication_Error)
    __arm_cp.6_1                             0x08003278   Number         4  modbus_master.o(.text.Handle_Communication_Error)
    __arm_cp.6_2                             0x0800327c   Number         4  modbus_master.o(.text.Handle_Communication_Error)
    [Anonymous Symbol]                       0x08003280   Section        0  stm32f1xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08003284   Section        0  oled_display.o(.text.I2C_Start)
    [Anonymous Symbol]                       0x080032bc   Section        0  oled_display.o(.text.I2C_Stop)
    [Anonymous Symbol]                       0x080032ec   Section        0  oled_display.o(.text.I2C_WriteByte)
    __arm_cp.3_0                             0x08003360   Number         4  oled_display.o(.text.I2C_WriteByte)
    __arm_cp.3_1                             0x08003364   Number         4  oled_display.o(.text.I2C_WriteByte)
    [Anonymous Symbol]                       0x08003368   Section        0  oled_display.o(.text.I2C_WriteBytes)
    [Anonymous Symbol]                       0x08003380   Section        0  key_handler.o(.text.Key_Init)
    [Anonymous Symbol]                       0x080033b8   Section        0  key_handler.o(.text.Key_Process)
    __arm_cp.4_1                             0x080034cc   Number         4  key_handler.o(.text.Key_Process)
    [Anonymous Symbol]                       0x080034d0   Section        0  key_handler.o(.text.Key_Scan_In_ISR)
    __arm_cp.0_0                             0x08003610   Number         4  key_handler.o(.text.Key_Scan_In_ISR)
    __arm_cp.0_1                             0x08003614   Number         4  key_handler.o(.text.Key_Scan_In_ISR)
    __arm_cp.0_2                             0x08003618   Number         4  key_handler.o(.text.Key_Scan_In_ISR)
    [Anonymous Symbol]                       0x0800361c   Section        0  system_status.o(.text.LED_Control)
    [Anonymous Symbol]                       0x08003640   Section        0  system_status.o(.text.LED_Update)
    __arm_cp.3_0                             0x08003684   Number         4  system_status.o(.text.LED_Update)
    __arm_cp.3_1                             0x08003688   Number         4  system_status.o(.text.LED_Update)
    [Anonymous Symbol]                       0x0800368c   Section        0  stm32f1xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x0800368e   Section        0  modbus_master.o(.text.Modbus_CRC16)
    [Anonymous Symbol]                       0x080036d0   Section        0  modbus_extended.o(.text.Modbus_Extended_Init)
    __arm_cp.0_1                             0x080037c4   Number         4  modbus_extended.o(.text.Modbus_Extended_Init)
    __arm_cp.0_3                             0x080037c8   Number         4  modbus_extended.o(.text.Modbus_Extended_Init)
    __arm_cp.0_4                             0x080037cc   Number         4  modbus_extended.o(.text.Modbus_Extended_Init)
    [Anonymous Symbol]                       0x080037d0   Section        0  modbus_extended.o(.text.Modbus_Extended_Process)
    [Anonymous Symbol]                       0x0800383c   Section        0  modbus_master.o(.text.Modbus_Master_Init)
    [Anonymous Symbol]                       0x08003854   Section        0  modbus_master.o(.text.Modbus_Master_Process)
    __arm_cp.2_0                             0x0800395c   Number         4  modbus_master.o(.text.Modbus_Master_Process)
    __arm_cp.2_1                             0x08003960   Number         4  modbus_master.o(.text.Modbus_Master_Process)
    __arm_cp.2_2                             0x08003964   Number         4  modbus_master.o(.text.Modbus_Master_Process)
    __arm_cp.2_3                             0x08003968   Number         4  modbus_master.o(.text.Modbus_Master_Process)
    [Anonymous Symbol]                       0x0800396c   Section        0  modbus_master.o(.text.Modbus_Master_Process_Response)
    [Anonymous Symbol]                       0x0800396e   Section        0  modbus_slave.o(.text.Modbus_Slave_Init)
    [Anonymous Symbol]                       0x08003972   Section        0  modbus_slave.o(.text.Modbus_Slave_Process)
    [Anonymous Symbol]                       0x08003976   Section        0  modbus_slave.o(.text.Modbus_Slave_Process_Frame)
    [Anonymous Symbol]                       0x0800397c   Section        0  stm32f1xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08003980   Section        0  oled_display.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x0800399c   Section        0  oled_display.o(.text.OLED_Display_Process)
    [Anonymous Symbol]                       0x080039a0   Section        0  oled_display.o(.text.OLED_FrameBuffer_Init)
    [Anonymous Symbol]                       0x080039c0   Section        0  oled_display.o(.text.OLED_FrameBuffer_SetPixel)
    [Anonymous Symbol]                       0x08003a00   Section        0  oled_display.o(.text.OLED_FrameBuffer_Update)
    __arm_cp.14_0                            0x08003a90   Number         4  oled_display.o(.text.OLED_FrameBuffer_Update)
    __arm_cp.14_1                            0x08003a94   Number         4  oled_display.o(.text.OLED_FrameBuffer_Update)
    [Anonymous Symbol]                       0x08003a98   Section        0  oled_display.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x08003b1c   Section        0  oled_display.o(.text.OLED_SetDisplayWindow)
    __arm_cp.15_0                            0x08003b48   Number         4  oled_display.o(.text.OLED_SetDisplayWindow)
    [Anonymous Symbol]                       0x08003b4c   Section        0  oled_display.o(.text.OLED_ShowChar)
    __arm_cp.21_0                            0x08003c44   Number         4  oled_display.o(.text.OLED_ShowChar)
    __arm_cp.21_1                            0x08003c48   Number         4  oled_display.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x08003c4c   Section        0  oled_display.o(.text.OLED_ShowChinese)
    [Anonymous Symbol]                       0x08003ce4   Section        0  oled_display.o(.text.OLED_ShowString)
    __arm_cp.22_0                            0x08003dc8   Number         4  oled_display.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x08003dcc   Section        0  oled_display.o(.text.OLED_WriteCmd)
    __arm_cp.1_0                             0x08003dec   Number         4  oled_display.o(.text.OLED_WriteCmd)
    [Anonymous Symbol]                       0x08003df0   Section        0  oled_display.o(.text.OLED_WriteDataBurst)
    [Anonymous Symbol]                       0x08003e1c   Section        0  modbus_master.o(.text.Parse_Response_Data)
    __arm_cp.5_0                             0x08003e40   Number         4  modbus_master.o(.text.Parse_Response_Data)
    [Anonymous Symbol]                       0x08003e44   Section        0  stm32f1xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08003e48   Section        0  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_1                             0x08004018   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_2                             0x0800401c   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_3                             0x08004020   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_4                             0x08004024   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_5                             0x08004028   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_6                             0x0800402c   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    __arm_cp.8_7                             0x08004030   Number         4  key_handler.o(.text.Process_Advanced_Setting_Menu)
    [Anonymous Symbol]                       0x08004034   Section        0  modbus_extended.o(.text.Process_Extended_Frame)
    __arm_cp.3_0                             0x08004200   Number         4  modbus_extended.o(.text.Process_Extended_Frame)
    [Anonymous Symbol]                       0x08004204   Section        0  key_handler.o(.text.Process_Master_Setting_Menu)
    __arm_cp.7_2                             0x08004358   Number         4  key_handler.o(.text.Process_Master_Setting_Menu)
    __arm_cp.7_3                             0x0800435c   Number         4  key_handler.o(.text.Process_Master_Setting_Menu)
    [Anonymous Symbol]                       0x08004360   Section        0  modbus_slave.o(.text.Process_Modbus_Frame)
    __arm_cp.4_0                             0x080043ec   Number         4  modbus_slave.o(.text.Process_Modbus_Frame)
    [Anonymous Symbol]                       0x080043f0   Section        0  key_handler.o(.text.Process_Password_Menu)
    __arm_cp.5_0                             0x08004478   Number         4  key_handler.o(.text.Process_Password_Menu)
    __arm_cp.5_1                             0x0800447c   Number         4  key_handler.o(.text.Process_Password_Menu)
    [Anonymous Symbol]                       0x08004480   Section        0  modbus_extended.o(.text.Process_Read_All_Objects)
    [Anonymous Symbol]                       0x080044e8   Section        0  modbus_slave.o(.text.Process_Read_Coils)
    __arm_cp.5_0                             0x080045c4   Number         4  modbus_slave.o(.text.Process_Read_Coils)
    [Anonymous Symbol]                       0x080045c8   Section        0  modbus_slave.o(.text.Process_Read_Holding_Registers)
    [Anonymous Symbol]                       0x080046b4   Section        0  modbus_extended.o(.text.Process_Read_Object)
    __arm_cp.5_1                             0x08004828   Number         4  modbus_extended.o(.text.Process_Read_Object)
    [Anonymous Symbol]                       0x0800482c   Section        0  key_handler.o(.text.Process_Slave_Setting_Menu)
    __arm_cp.6_0                             0x08004924   Number         4  key_handler.o(.text.Process_Slave_Setting_Menu)
    __arm_cp.6_2                             0x08004928   Number         4  key_handler.o(.text.Process_Slave_Setting_Menu)
    __arm_cp.6_3                             0x0800492c   Number         4  key_handler.o(.text.Process_Slave_Setting_Menu)
    [Anonymous Symbol]                       0x08004930   Section        0  modbus_extended.o(.text.Process_Write_Object)
    __arm_cp.6_0                             0x08004ab0   Number         4  modbus_extended.o(.text.Process_Write_Object)
    __arm_cp.6_1                             0x08004ab4   Number         4  modbus_extended.o(.text.Process_Write_Object)
    __arm_cp.6_2                             0x08004ab8   Number         4  modbus_extended.o(.text.Process_Write_Object)
    [Anonymous Symbol]                       0x08004abc   Section        0  modbus_slave.o(.text.Process_Write_Single_Register)
    __arm_cp.7_0                             0x08004bf8   Number         4  modbus_slave.o(.text.Process_Write_Single_Register)
    __arm_cp.7_2                             0x08004c0c   Number         4  modbus_slave.o(.text.Process_Write_Single_Register)
    [Anonymous Symbol]                       0x08004c10   Section        0  modbus_master.o(.text.RS485_Set_Mode)
    __arm_cp.1_0                             0x08004ca8   Number         4  modbus_master.o(.text.RS485_Set_Mode)
    __arm_cp.1_1                             0x08004cac   Number         4  modbus_master.o(.text.RS485_Set_Mode)
    [Anonymous Symbol]                       0x08004cb0   Section        0  modbus_slave.o(.text.RS485_Slave_Set_Mode)
    __arm_cp.1_0                             0x08004cc4   Number         4  modbus_slave.o(.text.RS485_Slave_Set_Mode)
    [Anonymous Symbol]                       0x08004cc8   Section        0  modbus_slave.o(.text.Read_Config_Register)
    __arm_cp.8_1                             0x08004d70   Number         4  modbus_slave.o(.text.Read_Config_Register)
    [Anonymous Symbol]                       0x08004d74   Section        0  key_handler.o(.text.Reset_Setting_Variables)
    Reset_Setting_Variables                  0x08004d75   Thumb Code    80  key_handler.o(.text.Reset_Setting_Variables)
    __arm_cp.3_0                             0x08004dbc   Number         4  key_handler.o(.text.Reset_Setting_Variables)
    [Anonymous Symbol]                       0x08004dc0   Section        0  stm32f1xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08004dc4   Section        0  config_manager.o(.text.Set_Moisture_Correction)
    __arm_cp.9_0                             0x08004e04   Number         4  config_manager.o(.text.Set_Moisture_Correction)
    __arm_cp.9_1                             0x08004e08   Number         4  config_manager.o(.text.Set_Moisture_Correction)
    [Anonymous Symbol]                       0x08004e0c   Section        0  config_manager.o(.text.Set_Pressure_Correction)
    [Anonymous Symbol]                       0x08004e50   Section        0  config_manager.o(.text.Set_SF6_Threshold)
    __arm_cp.7_0                             0x08004ea0   Number         4  config_manager.o(.text.Set_SF6_Threshold)
    __arm_cp.7_1                             0x08004ea4   Number         4  config_manager.o(.text.Set_SF6_Threshold)
    [Anonymous Symbol]                       0x08004ea8   Section        0  config_manager.o(.text.Set_Slave_Address)
    __arm_cp.5_0                             0x08004ed0   Number         4  config_manager.o(.text.Set_Slave_Address)
    [Anonymous Symbol]                       0x08004ed4   Section        0  config_manager.o(.text.Set_Slave_Baudrate)
    __arm_cp.6_0                             0x08004f00   Number         4  config_manager.o(.text.Set_Slave_Baudrate)
    [Anonymous Symbol]                       0x08004f04   Section        0  uart_dma.o(.text.Split_Modbus_Frames)
    [Anonymous Symbol]                       0x08004f5c   Section        0  stm32f1xx_it.o(.text.SysTick_Handler)
    __arm_cp.8_0                             0x08004f7c   Number         4  stm32f1xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08004f80   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08005008   Section        0  system_stm32f1xx_1.o(.text.SystemInit)
    [Anonymous Symbol]                       0x0800500c   Section        0  system_monitor.o(.text.System_Check_Communication)
    [Anonymous Symbol]                       0x0800502c   Section        0  system_monitor.o(.text.System_Get_Communication_Status)
    [Anonymous Symbol]                       0x08005038   Section        0  system_monitor.o(.text.System_Monitor_Init)
    [Anonymous Symbol]                       0x08005054   Section        0  system_monitor.o(.text.System_Monitor_Update)
    __arm_cp.1_0                             0x08005080   Number         4  system_monitor.o(.text.System_Monitor_Update)
    [Anonymous Symbol]                       0x08005084   Section        0  system_monitor.o(.text.System_Set_Communication_Status)
    __arm_cp.4_0                             0x08005090   Number         4  system_monitor.o(.text.System_Set_Communication_Status)
    [Anonymous Symbol]                       0x08005094   Section        0  system_status.o(.text.System_Status_Init)
    [Anonymous Symbol]                       0x080050a4   Section        0  system_status.o(.text.System_Status_Update)
    __arm_cp.2_0                             0x080050ec   Number         4  system_status.o(.text.System_Status_Update)
    __arm_cp.2_1                             0x080050f0   Number         4  system_status.o(.text.System_Status_Update)
    [Anonymous Symbol]                       0x080050f4   Section        0  modbus_extended.o(.text.TLV_Encode_DateTime)
    [Anonymous Symbol]                       0x0800511a   Section        0  uart_dma.o(.text.UART1_Frame_Received_Callback)
    [Anonymous Symbol]                       0x08005124   Section        0  uart_dma.o(.text.UART1_Send_Data)
    [Anonymous Symbol]                       0x08005168   Section        0  uart_dma.o(.text.UART1_Start_Receive)
    [Anonymous Symbol]                       0x080051a8   Section        0  uart_dma.o(.text.UART2_Start_Receive)
    [Anonymous Symbol]                       0x080051e8   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080051e9   Thumb Code    12  stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x080051f4   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAError                            0x080051f5   Thumb Code    78  stm32f1xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08005242   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005243   Thumb Code   140  stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x080052ce   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080052cf   Thumb Code    24  stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x080052e6   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x080052e7   Thumb Code    62  stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt)
    [Anonymous Symbol]                       0x08005324   Section        0  stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x08005325   Thumb Code     6  stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt)
    [Anonymous Symbol]                       0x0800532c   Section        0  uart_dma.o(.text.UART_DMA_Init)
    [Anonymous Symbol]                       0x08005368   Section        0  uart_dma.o(.text.UART_DMA_Process)
    __arm_cp.5_0                             0x0800541c   Number         4  uart_dma.o(.text.UART_DMA_Process)
    __arm_cp.5_1                             0x08005420   Number         4  uart_dma.o(.text.UART_DMA_Process)
    __arm_cp.5_2                             0x08005424   Number         4  uart_dma.o(.text.UART_DMA_Process)
    __arm_cp.5_3                             0x08005428   Number         4  uart_dma.o(.text.UART_DMA_Process)
    __arm_cp.5_4                             0x0800542c   Number         4  uart_dma.o(.text.UART_DMA_Process)
    [Anonymous Symbol]                       0x08005430   Section        0  stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005431   Thumb Code    78  stm32f1xx_hal_uart.o(.text.UART_EndRxTransfer)
    [Anonymous Symbol]                       0x0800547e   Section        0  stm32f1xx_hal_uart.o(.text.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x0800547f   Thumb Code    28  stm32f1xx_hal_uart.o(.text.UART_EndTxTransfer)
    [Anonymous Symbol]                       0x0800549a   Section        0  stm32f1xx_hal_uart.o(.text.UART_Receive_IT)
    UART_Receive_IT                          0x0800549b   Thumb Code   202  stm32f1xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08005564   Section        0  stm32f1xx_hal_uart.o(.text.UART_SetConfig)
    UART_SetConfig                           0x08005565   Thumb Code   132  stm32f1xx_hal_uart.o(.text.UART_SetConfig)
    __arm_cp.2_0                             0x080055e4   Number         4  stm32f1xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x080055e8   Section        0  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    __arm_cp.19_0                            0x0800566c   Number         4  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    __arm_cp.19_1                            0x08005670   Number         4  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    __arm_cp.19_2                            0x08005674   Number         4  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    [Anonymous Symbol]                       0x08005678   Section        0  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT)
    [Anonymous Symbol]                       0x080056aa   Section        0  stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080056ab   Thumb Code   112  stm32f1xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout)
    [Anonymous Symbol]                       0x0800571c   Section        0  stm32f1xx_it.o(.text.USART1_IRQHandler)
    __arm_cp.9_0                             0x08005724   Number         4  stm32f1xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08005728   Section        0  stm32f1xx_it.o(.text.USART2_IRQHandler)
    __arm_cp.10_0                            0x08005730   Number         4  stm32f1xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x08005734   Section        0  key_handler.o(.text.Update_Menu_Display)
    __arm_cp.9_0                             0x08005780   Number         4  key_handler.o(.text.Update_Menu_Display)
    __arm_cp.9_1                             0x08005784   Number         4  key_handler.o(.text.Update_Menu_Display)
    [Anonymous Symbol]                       0x08005788   Section        0  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    __arm_cp.21_0                            0x08005804   Number         4  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    __arm_cp.21_1                            0x08005808   Number         4  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    __arm_cp.21_2                            0x0800580c   Number         4  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    __arm_cp.21_3                            0x08005810   Number         4  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    __arm_cp.21_4                            0x08005814   Number         4  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    [Anonymous Symbol]                       0x08005818   Section        0  stm32f1xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x0800581c   Section        0  modbus_master.o(.text.Validate_Response)
    [Anonymous Symbol]                       0x0800585c   Section        0  config_manager.o(.text.Validate_Slave_Address)
    __arm_cp.4_0                             0x08005894   Number         4  config_manager.o(.text.Validate_Slave_Address)
    [Anonymous Symbol]                       0x08005898   Section        0  system_status.o(.text.Watchdog_Init)
    [Anonymous Symbol]                       0x0800589c   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x08005ad4   Number         4  main.o(.text.main)
    __arm_cp.0_1                             0x08005ad8   Number         4  main.o(.text.main)
    __arm_cp.0_2                             0x08005adc   Number         4  main.o(.text.main)
    __arm_cp.0_3                             0x08005ae0   Number         4  main.o(.text.main)
    __arm_cp.0_4                             0x08005ae4   Number         4  main.o(.text.main)
    __arm_cp.0_5                             0x08005ae8   Number         4  main.o(.text.main)
    __arm_cp.0_6                             0x08005aec   Number         4  main.o(.text.main)
    __arm_cp.0_7                             0x08005af0   Number         4  main.o(.text.main)
    __arm_cp.0_8                             0x08005af4   Number         4  main.o(.text.main)
    __arm_cp.0_9                             0x08005af8   Number         4  main.o(.text.main)
    __arm_cp.0_10                            0x08005afc   Number         4  main.o(.text.main)
    CL$$btod_d2e                             0x08005b00   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08005b3e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08005b84   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08005be4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08005f1c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08005ff8   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08006022   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800604c   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08006290   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080062b8   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x080062c8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x080062f4   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08006300   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08006358   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08006367   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmp                               0x0800641c   Section       84  fcmp.o(x$fpl$fcmp)
    x$fpl$fcmpinf                            0x08006470   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x08006488   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08006489   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$feqf                               0x0800660c   Section      104  feqf.o(x$fpl$feqf)
    x$fpl$ffix                               0x08006674   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x080066ac   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x080066ec   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x0800671c   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fgeqf                              0x08006744   Section      104  fgeqf.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x080067ac   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08006814   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08006916   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080069a2   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x080069ac   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080069bb   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x08006a96   Section        4  printf1.o(x$fpl$printf1)
    .constdata                               0x08006a9a   Section       40  _printf_hex_int.o(.constdata)
    x$fpl$usenofp                            0x08006a9a   Section        0  usenofp.o(x$fpl$usenofp)
    uc_hextab                                0x08006a9a   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08006aae   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08006ac2   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08006ac2   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08006ad4   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08006ad4   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08006b10   Data          64  bigflt0.o(.constdata)
    [Anonymous Symbol]                       0x08006b68   Section        0  key_handler.o(.rodata..Lswitch.table.30)
    .Lswitch.table.29                        0x08007d40   Data          16  key_handler.o(.rodata.cst16)
    .Lswitch.table                           0x08007d50   Data          16  modbus_extended.o(.rodata.cst16)
    [Anonymous Symbol]                       0x08007d60   Section        0  key_handler.o(.rodata.str1.1)
    .L.str.26                                0x08007d60   Data           5  key_handler.o(.rodata.str1.1)
    .L.str.27                                0x08007d65   Data           6  key_handler.o(.rodata.str1.1)
    .L.str.28                                0x08007d6b   Data           7  key_handler.o(.rodata.str1.1)
    .L.str.16                                0x08007d72   Data          13  key_handler.o(.rodata.str1.1)
    .L.str.13                                0x08007d7f   Data          21  key_handler.o(.rodata.str1.1)
    .L.str.15                                0x08007d94   Data          16  key_handler.o(.rodata.str1.1)
    locale$$data                             0x08007dc4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08007dc8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007dd0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08007ddc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08007dde   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08007ddf   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08007de0   Data           0  lc_numeric_c.o(locale$$data)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f1xx_hal.o(.data..L_MergedGlobals)
    .bss                                     0x20000010   Section       96  libspace.o(.bss)
    [Anonymous Symbol]                       0x20000070   Section        0  main.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200000f0   Section        0  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000140   Section        0  key_handler.o(.bss..L_MergedGlobals)
    force_refresh                            0x20000140   Data           1  key_handler.o(.bss..L_MergedGlobals)
    data_menu_index                          0x20000141   Data           1  key_handler.o(.bss..L_MergedGlobals)
    password_value                           0x20000142   Data           1  key_handler.o(.bss..L_MergedGlobals)
    k1_long_pressed                          0x20000143   Data           1  key_handler.o(.bss..L_MergedGlobals)
    k3_long_pressed                          0x20000144   Data           1  key_handler.o(.bss..L_MergedGlobals)
    setting_step                             0x20000145   Data           1  key_handler.o(.bss..L_MergedGlobals)
    temp_slave_baud                          0x20000146   Data           1  key_handler.o(.bss..L_MergedGlobals)
    temp_master_baud                         0x20000147   Data           1  key_handler.o(.bss..L_MergedGlobals)
    key_events                               0x20000148   Data           3  key_handler.o(.bss..L_MergedGlobals)
    key_event_flags                          0x2000014b   Data           3  key_handler.o(.bss..L_MergedGlobals)
    startup_timer                            0x20000150   Data           4  key_handler.o(.bss..L_MergedGlobals)
    data_display_timer                       0x20000154   Data           4  key_handler.o(.bss..L_MergedGlobals)
    temp_slave_addr                          0x20000158   Data           4  key_handler.o(.bss..L_MergedGlobals)
    temp_device_type                         0x2000015c   Data           4  key_handler.o(.bss..L_MergedGlobals)
    temp_master_addr                         0x20000160   Data           4  key_handler.o(.bss..L_MergedGlobals)
    temp_moisture_correction                 0x20000164   Data           8  key_handler.o(.bss..L_MergedGlobals)
    temp_alarm_threshold                     0x2000016c   Data          16  key_handler.o(.bss..L_MergedGlobals)
    temp_pressure_correction                 0x2000017c   Data          16  key_handler.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000018c   Section        0  modbus_master.o(.bss..L_MergedGlobals)
    current_channel                          0x2000018c   Data           1  modbus_master.o(.bss..L_MergedGlobals)
    last_poll_time                           0x20000190   Data           4  modbus_master.o(.bss..L_MergedGlobals)
    retry_count                              0x20000194   Data           4  modbus_master.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000198   Section        0  modbus_slave.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200001a0   Section        0  system_status.o(.bss..L_MergedGlobals)
    current_led_mode                         0x200001a0   Data           1  system_status.o(.bss..L_MergedGlobals)
    led_state                                0x200001a1   Data           1  system_status.o(.bss..L_MergedGlobals)
    System_Status_Update.last_update         0x200001a4   Data           4  system_status.o(.bss..L_MergedGlobals)
    led_timer                                0x200001a8   Data           4  system_status.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x200001b0   Section        0  modbus_extended.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000210   Section        0  main.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x20000260   Section        0  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x200002b0   Section        0  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.2)
    [Anonymous Symbol]                       0x200002f4   Section        0  modbus_slave.o(.bss..L_MergedGlobals.2)
    rx_byte                                  0x200002f4   Data           1  modbus_slave.o(.bss..L_MergedGlobals.2)
    HAL_UART_RxCpltCallback.total_bytes      0x200002f8   Data           4  modbus_slave.o(.bss..L_MergedGlobals.2)
    [Anonymous Symbol]                       0x20000300   Section        0  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.3)
    [Anonymous Symbol]                       0x20000350   Section        0  key_handler.o(.bss..L_MergedGlobals.31)
    key_info                                 0x20000350   Data          60  key_handler.o(.bss..L_MergedGlobals.31)
    [Anonymous Symbol]                       0x2000038c   Section        0  stm32f1xx_it.o(.bss.SysTick_Handler.key_scan_counter)
    SysTick_Handler.key_scan_counter         0x2000038c   Data           4  stm32f1xx_it.o(.bss.SysTick_Handler.key_scan_counter)
    [Anonymous Symbol]                       0x20000390   Section        0  uart_dma.o(.bss.UART_DMA_Process.last_check_time)
    UART_DMA_Process.last_check_time         0x20000390   Data           4  uart_dma.o(.bss.UART_DMA_Process.last_check_time)
    [Anonymous Symbol]                       0x20000394   Section        0  system_monitor.o(.bss.channel_comm_status)
    channel_comm_status                      0x20000394   Data           4  system_monitor.o(.bss.channel_comm_status)
    [Anonymous Symbol]                       0x200011ac   Section        0  main.o(.bss.main.last_sf6_update)
    main.last_sf6_update                     0x200011ac   Data           4  main.o(.bss.main.last_sf6_update)
    HEAP                                     0x200014d0   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x200014d0   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x200016d0   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x200016d0   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20001ad0   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000161   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800016d   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x08000173   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x08000179   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800017f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000183   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000195   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000197   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000199   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800019b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800019b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001a1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001a1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001a5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001a5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001ad   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001af   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001af   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001b3   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001b9   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x080001d3   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x080001d5   Thumb Code     0  startup_stm32f103xb.o(.text)
    __2sprintf                               0x080001f9   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08000221   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800024d   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x0800026f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080002c1   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x08000339   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08000339   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000391   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strlen                                   0x08000519   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x08000557   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000557   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080005bd   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr                           0x080005e1   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080005e1   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080005e5   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000625   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000625   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000625   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000629   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000673   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000675   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000677   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x08000679   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800072b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080008dd   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000b53   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000b79   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000b83   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000b97   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000ba7   Thumb Code     8  _printf_char.o(.text)
    __aeabi_memcpy4                          0x08000baf   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000baf   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000baf   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000bf7   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_locale                              0x08000c15   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000c1d   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000ca9   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000d29   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x08000e0d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000e0d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000e0d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000e15   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000e5f   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000e71   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08000ef1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000efd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000efd   Thumb Code     2  use_no_semi.o(.text)
    ADC_ConversionStop_Disable               0x08000eff   Thumb Code    82  stm32f1xx_hal_adc.o(.text.ADC_ConversionStop_Disable)
    __semihosting_library_function           0x08000eff   Thumb Code     0  indicate_semi.o(.text)
    Apply_Data_Corrections                   0x08000f51   Thumb Code   132  config_manager.o(.text.Apply_Data_Corrections)
    BusFault_Handler                         0x08000fcd   Thumb Code     2  stm32f1xx_it.o(.text.BusFault_Handler)
    Config_Init                              0x08000fd1   Thumb Code   188  config_manager.o(.text.Config_Init)
    Config_Save                              0x08001085   Thumb Code    76  config_manager.o(.text.Config_Save)
    DMA1_Channel4_IRQHandler                 0x080010cd   Thumb Code    12  stm32f1xx_it.o(.text.DMA1_Channel4_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x080010d9   Thumb Code    12  stm32f1xx_it.o(.text.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x080010e5   Thumb Code    12  stm32f1xx_it.o(.text.DMA1_Channel6_IRQHandler)
    DMA1_Channel7_IRQHandler                 0x080010f1   Thumb Code    12  stm32f1xx_it.o(.text.DMA1_Channel7_IRQHandler)
    DebugMon_Handler                         0x08001125   Thumb Code     2  stm32f1xx_it.o(.text.DebugMon_Handler)
    Display_Address_Screen                   0x08001129   Thumb Code   320  key_handler.o(.text.Display_Address_Screen)
    Display_Data_Screen                      0x08001269   Thumb Code   348  key_handler.o(.text.Display_Data_Screen)
    Display_Password_Screen                  0x080013c5   Thumb Code    64  key_handler.o(.text.Display_Password_Screen)
    Display_Setting_Screen                   0x08001405   Thumb Code   616  key_handler.o(.text.Display_Setting_Screen)
    Display_Startup_Screen                   0x0800166d   Thumb Code    92  key_handler.o(.text.Display_Startup_Screen)
    Error_Handler                            0x080016c9   Thumb Code     8  main.o(.text.Error_Handler)
    FLASH_WaitForLastOperation               0x08001725   Thumb Code    96  stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation)
    Get_Corrected_Register_Value             0x08001785   Thumb Code    32  config_manager.o(.text.Get_Corrected_Register_Value)
    HAL_ADC_ConfigChannel                    0x080017a5   Thumb Code   280  stm32f1xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    HAL_ADC_Init                             0x080018bd   Thumb Code   272  stm32f1xx_hal_adc.o(.text.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x080019cd   Thumb Code    92  stm32f1xx_hal_msp.o(.text.HAL_ADC_MspInit)
    HAL_DMA_Abort                            0x08001a29   Thumb Code    62  stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001a69   Thumb Code   164  stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001af5   Thumb Code   320  stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001c35   Thumb Code   116  stm32f1xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001ca9   Thumb Code   110  stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001d19   Thumb Code    44  stm32f1xx_hal.o(.text.HAL_Delay)
    HAL_Delay_us                             0x08001d45   Thumb Code    40  oled_display.o(.text.HAL_Delay_us)
    HAL_FLASHEx_Erase                        0x08001d6d   Thumb Code   192  stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08001e25   Thumb Code    20  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x08001e35   Thumb Code   176  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x08001ee5   Thumb Code    44  stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock)
    HAL_GPIO_DeInit                          0x08001f11   Thumb Code   244  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit)
    HAL_GPIO_Init                            0x08001fe9   Thumb Code   452  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080021a9   Thumb Code    10  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080021b3   Thumb Code    10  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080021bd   Thumb Code    12  stm32f1xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x080021c5   Thumb Code    16  stm32f1xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080021d1   Thumb Code    36  stm32f1xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x080021f5   Thumb Code    64  stm32f1xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08002231   Thumb Code    64  stm32f1xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_DisableIRQ                      0x08002271   Thumb Code    36  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ)
    HAL_NVIC_EnableIRQ                       0x08002295   Thumb Code    28  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080022b1   Thumb Code    92  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002309   Thumb Code    32  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_PeriphCLKConfig                0x08002329   Thumb Code   256  stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002425   Thumb Code   312  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002559   Thumb Code    32  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800256d   Thumb Code    32  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002589   Thumb Code    88  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080025e1   Thumb Code   896  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002961   Thumb Code    40  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_UARTEx_RxEventCallback               0x08002989   Thumb Code     2  stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x0800298b   Thumb Code   116  stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop)
    HAL_UART_DeInit                          0x080029ff   Thumb Code    54  stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit)
    HAL_UART_ErrorCallback                   0x08002a35   Thumb Code    96  uart_dma.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002a8d   Thumb Code   676  stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002d31   Thumb Code    94  stm32f1xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspDeInit                       0x08002d91   Thumb Code    88  stm32f1xx_hal_msp.o(.text.HAL_UART_MspDeInit)
    HAL_UART_MspInit                         0x08002de1   Thumb Code   428  stm32f1xx_hal_msp.o(.text.HAL_UART_MspInit)
    HAL_UART_Receive                         0x08002f89   Thumb Code   190  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive)
    HAL_UART_Receive_DMA                     0x08003047   Thumb Code    32  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA)
    HAL_UART_Receive_IT                      0x08003067   Thumb Code    32  stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08003089   Thumb Code    88  modbus_slave.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x080030e1   Thumb Code     2  stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x080030e3   Thumb Code   174  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_Transmit_DMA                    0x08003191   Thumb Code   116  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x08003205   Thumb Code    44  uart_dma.o(.text.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x08003231   Thumb Code     2  stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback)
    Handle_Communication_Error               0x08003235   Thumb Code    76  modbus_master.o(.text.Handle_Communication_Error)
    HardFault_Handler                        0x08003281   Thumb Code     2  stm32f1xx_it.o(.text.HardFault_Handler)
    I2C_Start                                0x08003285   Thumb Code    64  oled_display.o(.text.I2C_Start)
    I2C_Stop                                 0x080032bd   Thumb Code    56  oled_display.o(.text.I2C_Stop)
    I2C_WriteByte                            0x080032ed   Thumb Code   124  oled_display.o(.text.I2C_WriteByte)
    I2C_WriteBytes                           0x08003369   Thumb Code    24  oled_display.o(.text.I2C_WriteBytes)
    Key_Init                                 0x08003381   Thumb Code    68  key_handler.o(.text.Key_Init)
    Key_Process                              0x080033b9   Thumb Code   284  key_handler.o(.text.Key_Process)
    Key_Scan_In_ISR                          0x080034d1   Thumb Code   332  key_handler.o(.text.Key_Scan_In_ISR)
    LED_Control                              0x0800361d   Thumb Code    44  system_status.o(.text.LED_Control)
    LED_Update                               0x08003641   Thumb Code    76  system_status.o(.text.LED_Update)
    MemManage_Handler                        0x0800368d   Thumb Code     2  stm32f1xx_it.o(.text.MemManage_Handler)
    Modbus_CRC16                             0x0800368f   Thumb Code    66  modbus_master.o(.text.Modbus_CRC16)
    Modbus_Extended_Init                     0x080036d1   Thumb Code   260  modbus_extended.o(.text.Modbus_Extended_Init)
    Modbus_Extended_Process                  0x080037d1   Thumb Code   112  modbus_extended.o(.text.Modbus_Extended_Process)
    Modbus_Master_Init                       0x0800383d   Thumb Code    22  modbus_master.o(.text.Modbus_Master_Init)
    Modbus_Master_Process                    0x08003855   Thumb Code   280  modbus_master.o(.text.Modbus_Master_Process)
    Modbus_Master_Process_Response           0x0800396d   Thumb Code     2  modbus_master.o(.text.Modbus_Master_Process_Response)
    Modbus_Slave_Init                        0x0800396f   Thumb Code     4  modbus_slave.o(.text.Modbus_Slave_Init)
    Modbus_Slave_Process                     0x08003973   Thumb Code     4  modbus_slave.o(.text.Modbus_Slave_Process)
    Modbus_Slave_Process_Frame               0x08003977   Thumb Code     6  modbus_slave.o(.text.Modbus_Slave_Process_Frame)
    NMI_Handler                              0x0800397d   Thumb Code     2  stm32f1xx_it.o(.text.NMI_Handler)
    OLED_Clear                               0x08003981   Thumb Code    32  oled_display.o(.text.OLED_Clear)
    OLED_Display_Process                     0x0800399d   Thumb Code     4  oled_display.o(.text.OLED_Display_Process)
    OLED_FrameBuffer_Init                    0x080039a1   Thumb Code    36  oled_display.o(.text.OLED_FrameBuffer_Init)
    OLED_FrameBuffer_SetPixel                0x080039c1   Thumb Code    68  oled_display.o(.text.OLED_FrameBuffer_SetPixel)
    OLED_FrameBuffer_Update                  0x08003a01   Thumb Code   152  oled_display.o(.text.OLED_FrameBuffer_Update)
    OLED_Init                                0x08003a99   Thumb Code   136  oled_display.o(.text.OLED_Init)
    OLED_SetDisplayWindow                    0x08003b1d   Thumb Code    48  oled_display.o(.text.OLED_SetDisplayWindow)
    OLED_ShowChar                            0x08003b4d   Thumb Code   256  oled_display.o(.text.OLED_ShowChar)
    OLED_ShowChinese                         0x08003c4d   Thumb Code   152  oled_display.o(.text.OLED_ShowChinese)
    OLED_ShowString                          0x08003ce5   Thumb Code   232  oled_display.o(.text.OLED_ShowString)
    OLED_WriteCmd                            0x08003dcd   Thumb Code    36  oled_display.o(.text.OLED_WriteCmd)
    OLED_WriteDataBurst                      0x08003df1   Thumb Code    42  oled_display.o(.text.OLED_WriteDataBurst)
    Parse_Response_Data                      0x08003e1d   Thumb Code    40  modbus_master.o(.text.Parse_Response_Data)
    PendSV_Handler                           0x08003e45   Thumb Code     2  stm32f1xx_it.o(.text.PendSV_Handler)
    Process_Advanced_Setting_Menu            0x08003e49   Thumb Code   500  key_handler.o(.text.Process_Advanced_Setting_Menu)
    Process_Extended_Frame                   0x08004035   Thumb Code   464  modbus_extended.o(.text.Process_Extended_Frame)
    Process_Master_Setting_Menu              0x08004205   Thumb Code   356  key_handler.o(.text.Process_Master_Setting_Menu)
    Process_Modbus_Frame                     0x08004361   Thumb Code   144  modbus_slave.o(.text.Process_Modbus_Frame)
    Process_Password_Menu                    0x080043f1   Thumb Code   144  key_handler.o(.text.Process_Password_Menu)
    Process_Read_All_Objects                 0x08004481   Thumb Code   108  modbus_extended.o(.text.Process_Read_All_Objects)
    Process_Read_Coils                       0x080044e9   Thumb Code   224  modbus_slave.o(.text.Process_Read_Coils)
    Process_Read_Holding_Registers           0x080045c9   Thumb Code   234  modbus_slave.o(.text.Process_Read_Holding_Registers)
    Process_Read_Object                      0x080046b5   Thumb Code   380  modbus_extended.o(.text.Process_Read_Object)
    Process_Slave_Setting_Menu               0x0800482d   Thumb Code   264  key_handler.o(.text.Process_Slave_Setting_Menu)
    Process_Write_Object                     0x08004931   Thumb Code   396  modbus_extended.o(.text.Process_Write_Object)
    Process_Write_Single_Register            0x08004abd   Thumb Code   340  modbus_slave.o(.text.Process_Write_Single_Register)
    RS485_Set_Mode                           0x08004c11   Thumb Code   160  modbus_master.o(.text.RS485_Set_Mode)
    RS485_Slave_Set_Mode                     0x08004cb1   Thumb Code    24  modbus_slave.o(.text.RS485_Slave_Set_Mode)
    Read_Config_Register                     0x08004cc9   Thumb Code   176  modbus_slave.o(.text.Read_Config_Register)
    SVC_Handler                              0x08004dc1   Thumb Code     2  stm32f1xx_it.o(.text.SVC_Handler)
    Set_Moisture_Correction                  0x08004dc5   Thumb Code    76  config_manager.o(.text.Set_Moisture_Correction)
    Set_Pressure_Correction                  0x08004e0d   Thumb Code    80  config_manager.o(.text.Set_Pressure_Correction)
    Set_SF6_Threshold                        0x08004e51   Thumb Code    92  config_manager.o(.text.Set_SF6_Threshold)
    Set_Slave_Address                        0x08004ea9   Thumb Code    44  config_manager.o(.text.Set_Slave_Address)
    Set_Slave_Baudrate                       0x08004ed5   Thumb Code    48  config_manager.o(.text.Set_Slave_Baudrate)
    Split_Modbus_Frames                      0x08004f05   Thumb Code    88  uart_dma.o(.text.Split_Modbus_Frames)
    SysTick_Handler                          0x08004f5d   Thumb Code    36  stm32f1xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08004f81   Thumb Code   136  main.o(.text.SystemClock_Config)
    SystemInit                               0x08005009   Thumb Code     2  system_stm32f1xx_1.o(.text.SystemInit)
    System_Check_Communication               0x0800500d   Thumb Code    36  system_monitor.o(.text.System_Check_Communication)
    System_Get_Communication_Status          0x0800502d   Thumb Code    16  system_monitor.o(.text.System_Get_Communication_Status)
    System_Monitor_Init                      0x08005039   Thumb Code    36  system_monitor.o(.text.System_Monitor_Init)
    System_Monitor_Update                    0x08005055   Thumb Code    48  system_monitor.o(.text.System_Monitor_Update)
    System_Set_Communication_Status          0x08005085   Thumb Code    16  system_monitor.o(.text.System_Set_Communication_Status)
    System_Status_Init                       0x08005095   Thumb Code    24  system_status.o(.text.System_Status_Init)
    System_Status_Update                     0x080050a5   Thumb Code    80  system_status.o(.text.System_Status_Update)
    TLV_Encode_DateTime                      0x080050f5   Thumb Code    38  modbus_extended.o(.text.TLV_Encode_DateTime)
    UART1_Frame_Received_Callback            0x0800511b   Thumb Code    10  uart_dma.o(.text.UART1_Frame_Received_Callback)
    UART1_Send_Data                          0x08005125   Thumb Code    76  uart_dma.o(.text.UART1_Send_Data)
    UART1_Start_Receive                      0x08005169   Thumb Code    72  uart_dma.o(.text.UART1_Start_Receive)
    UART2_Start_Receive                      0x080051a9   Thumb Code    72  uart_dma.o(.text.UART2_Start_Receive)
    UART_DMA_Init                            0x0800532d   Thumb Code    76  uart_dma.o(.text.UART_DMA_Init)
    UART_DMA_Process                         0x08005369   Thumb Code   200  uart_dma.o(.text.UART_DMA_Process)
    UART_Start_Receive_DMA                   0x080055e9   Thumb Code   144  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08005679   Thumb Code    50  stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x0800571d   Thumb Code    12  stm32f1xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x08005729   Thumb Code    12  stm32f1xx_it.o(.text.USART2_IRQHandler)
    Update_Menu_Display                      0x08005735   Thumb Code    84  key_handler.o(.text.Update_Menu_Display)
    Update_SF6_Data_From_Modbus              0x08005789   Thumb Code   144  modbus_extended.o(.text.Update_SF6_Data_From_Modbus)
    UsageFault_Handler                       0x08005819   Thumb Code     2  stm32f1xx_it.o(.text.UsageFault_Handler)
    Validate_Response                        0x0800581d   Thumb Code    68  modbus_master.o(.text.Validate_Response)
    Validate_Slave_Address                   0x0800585d   Thumb Code    60  config_manager.o(.text.Validate_Slave_Address)
    Watchdog_Init                            0x08005899   Thumb Code     2  system_status.o(.text.Watchdog_Init)
    main                                     0x0800589d   Thumb Code   612  main.o(.text.main)
    _btod_d2e                                0x08005b01   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08005b3f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08005b85   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08005be5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08005f1d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08005ff9   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08006023   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800604d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08006291   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080062b9   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x080062c9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x080062f5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08006301   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08006301   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08006359   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08006359   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fcmpeq                           0x0800641d   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _feq                                     0x0800641d   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    _fneq                                    0x0800642b   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpgt                           0x08006439   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgr                                     0x08006439   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpge                           0x08006447   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgeq                                    0x08006447   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmple                           0x08006455   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fleq                                    0x08006455   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmplt                           0x08006463   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fls                                     0x08006463   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __fpl_fcmp_Inf                           0x08006471   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x08006489   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08006489   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_cfcmpeq                          0x0800660d   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x0800660d   Thumb Code   104  feqf.o(x$fpl$feqf)
    __aeabi_f2iz                             0x08006675   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08006675   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x080066ad   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080066ad   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x080066ed   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080066ed   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x0800671d   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x0800671d   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    _fcmpge                                  0x08006745   Thumb Code   104  fgeqf.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x080067ad   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080067ad   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x080067ff   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08006815   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08006815   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08006917   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080069a3   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x080069ad   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080069ad   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x08006a97   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x08006a9a   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08006b74   Data          16  system_stm32f1xx_1.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08006b84   Data           8  system_stm32f1xx_1.o(.rodata.APBPrescTable)
    ASCII_8x16                               0x08006b8c   Data        1456  font.o(.rodata.ASCII_8x16)
    ASCII_8x8                                0x0800713c   Data         728  font.o(.rodata.ASCII_8x8)
    Chinese_16x16                            0x08007414   Data        2345  font.o(.rodata.Chinese_16x16)
    Region$$Table$$Base                      0x08007da4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007dc4   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data..L_MergedGlobals)
    uwTick                                   0x20000008   Data           4  stm32f1xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx_1.o(.data.SystemCoreClock)
    __libspace_start                         0x20000010   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000070   Data           0  libspace.o(.bss)
    hadc1                                    0x20000070   Data          48  main.o(.bss..L_MergedGlobals)
    huart1                                   0x200000a0   Data          72  main.o(.bss..L_MergedGlobals)
    hdma_usart1_rx                           0x200000f0   Data          68  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals)
    rx_index                                 0x20000198   Data           1  modbus_slave.o(.bss..L_MergedGlobals)
    last_rx_time                             0x2000019c   Data           4  modbus_slave.o(.bss..L_MergedGlobals)
    g_device_info                            0x200001b0   Data          39  modbus_extended.o(.bss..L_MergedGlobals)
    g_comm_params                            0x200001d8   Data          48  modbus_extended.o(.bss..L_MergedGlobals)
    huart2                                   0x20000210   Data          72  main.o(.bss..L_MergedGlobals.1)
    hdma_usart1_tx                           0x20000260   Data          68  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.1)
    hdma_usart2_rx                           0x200002b0   Data          68  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.2)
    hdma_usart2_tx                           0x20000300   Data          68  stm32f1xx_hal_msp.o(.bss..L_MergedGlobals.3)
    coil_data                                0x20000398   Data         128  config_manager.o(.bss.coil_data)
    corrected_data                           0x20000418   Data         512  config_manager.o(.bss.corrected_data)
    current_menu                             0x20000618   Data           1  key_handler.o(.bss.current_menu)
    g_config                                 0x2000061c   Data         124  config_manager.o(.bss.g_config)
    g_ext_debug                              0x20000698   Data          12  modbus_extended.o(.bss.g_ext_debug)
    g_oled_fb                                0x200006a4   Data        1040  oled_display.o(.bss.g_oled_fb)
    g_sf6_data                               0x20000ab4   Data         144  modbus_extended.o(.bss.g_sf6_data)
    g_system_state                           0x20000b44   Data           1  system_status.o(.bss.g_system_state)
    g_system_status                          0x20000b48   Data          76  system_monitor.o(.bss.g_system_status)
    g_uart1_data                             0x20000b94   Data         780  uart_dma.o(.bss.g_uart1_data)
    g_uart2_data                             0x20000ea0   Data         780  uart_dma.o(.bss.g_uart2_data)
    modbus_data                              0x200011b0   Data         512  config_manager.o(.bss.modbus_data)
    pFlash                                   0x200013b0   Data          32  stm32f1xx_hal_flash.o(.bss.pFlash)
    rx_buffer                                0x200013d0   Data         256  modbus_slave.o(.bss.rx_buffer)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007df0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007de0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         1183  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         1451    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         1453    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         1455    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         1172    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         1171    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000166   0x08000166   0x00000006   Code   RO         1169    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800016c   0x0800016c   0x00000006   Code   RO         1170    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000172   0x08000172   0x00000006   Code   RO         1168    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000178   0x08000178   0x00000006   Code   RO         1167    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800017e   0x0800017e   0x00000004   Code   RO         1228    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000182   0x08000182   0x00000002   Code   RO         1318    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000184   0x08000184   0x00000000   Code   RO         1320    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         1322    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         1325    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         1327    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         1329    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000006   Code   RO         1330    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         1332    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         1334    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         1336    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x0000000a   Code   RO         1337    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1338    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1340    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1342    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1344    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1346    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1348    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1350    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1352    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1356    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1358    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1360    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1362    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000194   0x08000194   0x00000002   Code   RO         1363    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000196   0x08000196   0x00000002   Code   RO         1397    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1408    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1410    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1413    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1416    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1418    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000000   Code   RO         1421    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000198   0x08000198   0x00000002   Code   RO         1422    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         1217    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         1245    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000006   Code   RO         1257    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001a0   0x080001a0   0x00000000   Code   RO         1247    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001a0   0x080001a0   0x00000004   Code   RO         1248    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         1250    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001a4   0x080001a4   0x00000008   Code   RO         1251    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000002   Code   RO         1364    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ae   0x080001ae   0x00000000   Code   RO         1371    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ae   0x080001ae   0x00000004   Code   RO         1372    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001b2   0x080001b2   0x00000006   Code   RO         1373    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001b8   0x080001b8   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x080001f8   0x080001f8   0x00000028   Code   RO         1116    .text               c_w.l(noretval__2sprintf.o)
    0x08000220   0x08000220   0x0000004e   Code   RO         1120    .text               c_w.l(_printf_pad.o)
    0x0800026e   0x0800026e   0x00000052   Code   RO         1122    .text               c_w.l(_printf_str.o)
    0x080002c0   0x080002c0   0x00000078   Code   RO         1124    .text               c_w.l(_printf_dec.o)
    0x08000338   0x08000338   0x00000058   Code   RO         1129    .text               c_w.l(_printf_hex_int.o)
    0x08000390   0x08000390   0x00000188   Code   RO         1164    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000518   0x08000518   0x0000003e   Code   RO         1173    .text               c_w.l(strlen.o)
    0x08000556   0x08000556   0x0000008a   Code   RO         1175    .text               c_w.l(rt_memcpy_v6.o)
    0x080005e0   0x080005e0   0x00000044   Code   RO         1177    .text               c_w.l(rt_memclr.o)
    0x08000624   0x08000624   0x0000004e   Code   RO         1179    .text               c_w.l(rt_memclr_w.o)
    0x08000672   0x08000672   0x00000006   Code   RO         1181    .text               c_w.l(heapauxi.o)
    0x08000678   0x08000678   0x000000b2   Code   RO         1218    .text               c_w.l(_printf_intcommon.o)
    0x0800072a   0x0800072a   0x0000041e   Code   RO         1220    .text               c_w.l(_printf_fp_dec.o)
    0x08000b48   0x08000b48   0x00000030   Code   RO         1222    .text               c_w.l(_printf_char_common.o)
    0x08000b78   0x08000b78   0x0000000a   Code   RO         1224    .text               c_w.l(_sputc.o)
    0x08000b82   0x08000b82   0x0000002c   Code   RO         1226    .text               c_w.l(_printf_char.o)
    0x08000bae   0x08000bae   0x00000064   Code   RO         1229    .text               c_w.l(rt_memcpy_w.o)
    0x08000c12   0x08000c12   0x00000002   PAD
    0x08000c14   0x08000c14   0x00000008   Code   RO         1262    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000c1c   0x08000c1c   0x0000008a   Code   RO         1264    .text               c_w.l(lludiv10.o)
    0x08000ca6   0x08000ca6   0x00000002   PAD
    0x08000ca8   0x08000ca8   0x00000080   Code   RO         1266    .text               c_w.l(_printf_fp_infnan.o)
    0x08000d28   0x08000d28   0x000000e4   Code   RO         1270    .text               c_w.l(bigflt0.o)
    0x08000e0c   0x08000e0c   0x00000008   Code   RO         1304    .text               c_w.l(libspace.o)
    0x08000e14   0x08000e14   0x0000004a   Code   RO         1307    .text               c_w.l(sys_stackheap_outer.o)
    0x08000e5e   0x08000e5e   0x00000012   Code   RO         1309    .text               c_w.l(exit.o)
    0x08000e70   0x08000e70   0x00000080   Code   RO         1311    .text               c_w.l(strcmpv7m.o)
    0x08000ef0   0x08000ef0   0x0000000c   Code   RO         1383    .text               c_w.l(sys_exit.o)
    0x08000efc   0x08000efc   0x00000002   Code   RO         1398    .text               c_w.l(use_no_semi.o)
    0x08000efe   0x08000efe   0x00000000   Code   RO         1400    .text               c_w.l(indicate_semi.o)
    0x08000efe   0x08000efe   0x00000052   Code   RO          502    .text.ADC_ConversionStop_Disable  stm32f1xx_hal_adc.o
    0x08000f50   0x08000f50   0x0000007c   Code   RO          119    .text.Apply_Data_Corrections  config_manager.o
    0x08000fcc   0x08000fcc   0x00000002   Code   RO           38    .text.BusFault_Handler  stm32f1xx_it.o
    0x08000fce   0x08000fce   0x00000002   PAD
    0x08000fd0   0x08000fd0   0x000000b4   Code   RO           99    .text.Config_Init   config_manager.o
    0x08001084   0x08001084   0x00000048   Code   RO          103    .text.Config_Save   config_manager.o
    0x080010cc   0x080010cc   0x0000000c   Code   RO           54    .text.DMA1_Channel4_IRQHandler  stm32f1xx_it.o
    0x080010d8   0x080010d8   0x0000000c   Code   RO           56    .text.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x080010e4   0x080010e4   0x0000000c   Code   RO           58    .text.DMA1_Channel6_IRQHandler  stm32f1xx_it.o
    0x080010f0   0x080010f0   0x0000000c   Code   RO           60    .text.DMA1_Channel7_IRQHandler  stm32f1xx_it.o
    0x080010fc   0x080010fc   0x00000028   Code   RO          751    .text.DMA_SetConfig  stm32f1xx_hal_dma.o
    0x08001124   0x08001124   0x00000002   Code   RO           44    .text.DebugMon_Handler  stm32f1xx_it.o
    0x08001126   0x08001126   0x00000002   PAD
    0x08001128   0x08001128   0x00000140   Code   RO          175    .text.Display_Address_Screen  key_handler.o
    0x08001268   0x08001268   0x0000015c   Code   RO          173    .text.Display_Data_Screen  key_handler.o
    0x080013c4   0x080013c4   0x00000040   Code   RO          177    .text.Display_Password_Screen  key_handler.o
    0x08001404   0x08001404   0x00000268   Code   RO          179    .text.Display_Setting_Screen  key_handler.o
    0x0800166c   0x0800166c   0x0000005c   Code   RO          171    .text.Display_Startup_Screen  key_handler.o
    0x080016c8   0x080016c8   0x00000008   Code   RO           15    .text.Error_Handler  main.o
    0x080016d0   0x080016d0   0x00000054   Code   RO          878    .text.FLASH_SetErrorCode  stm32f1xx_hal_flash.o
    0x08001724   0x08001724   0x00000060   Code   RO          872    .text.FLASH_WaitForLastOperation  stm32f1xx_hal_flash.o
    0x08001784   0x08001784   0x00000020   Code   RO          121    .text.Get_Corrected_Register_Value  config_manager.o
    0x080017a4   0x080017a4   0x00000118   Code   RO          544    .text.HAL_ADC_ConfigChannel  stm32f1xx_hal_adc.o
    0x080018bc   0x080018bc   0x00000110   Code   RO          498    .text.HAL_ADC_Init  stm32f1xx_hal_adc.o
    0x080019cc   0x080019cc   0x0000005c   Code   RO           76    .text.HAL_ADC_MspInit  stm32f1xx_hal_msp.o
    0x08001a28   0x08001a28   0x0000003e   Code   RO          755    .text.HAL_DMA_Abort  stm32f1xx_hal_dma.o
    0x08001a66   0x08001a66   0x00000002   PAD
    0x08001a68   0x08001a68   0x0000008c   Code   RO          757    .text.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08001af4   0x08001af4   0x00000140   Code   RO          761    .text.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08001c34   0x08001c34   0x00000074   Code   RO          745    .text.HAL_DMA_Init  stm32f1xx_hal_dma.o
    0x08001ca8   0x08001ca8   0x0000006e   Code   RO          753    .text.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08001d16   0x08001d16   0x00000002   PAD
    0x08001d18   0x08001d18   0x0000002c   Code   RO          620    .text.HAL_Delay     stm32f1xx_hal.o
    0x08001d44   0x08001d44   0x00000028   Code   RO          260    .text.HAL_Delay_us  oled_display.o
    0x08001d6c   0x08001d6c   0x000000b8   Code   RO          908    .text.HAL_FLASHEx_Erase  stm32f1xx_hal_flash_ex.o
    0x08001e24   0x08001e24   0x00000010   Code   RO          886    .text.HAL_FLASH_Lock  stm32f1xx_hal_flash.o
    0x08001e34   0x08001e34   0x000000b0   Code   RO          870    .text.HAL_FLASH_Program  stm32f1xx_hal_flash.o
    0x08001ee4   0x08001ee4   0x0000002c   Code   RO          884    .text.HAL_FLASH_Unlock  stm32f1xx_hal_flash.o
    0x08001f10   0x08001f10   0x000000d8   Code   RO          720    .text.HAL_GPIO_DeInit  stm32f1xx_hal_gpio.o
    0x08001fe8   0x08001fe8   0x000001c0   Code   RO          718    .text.HAL_GPIO_Init  stm32f1xx_hal_gpio.o
    0x080021a8   0x080021a8   0x0000000a   Code   RO          722    .text.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x080021b2   0x080021b2   0x0000000a   Code   RO          724    .text.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080021bc   0x080021bc   0x00000008   Code   RO          612    .text.HAL_GetTick   stm32f1xx_hal.o
    0x080021c4   0x080021c4   0x0000000c   Code   RO          610    .text.HAL_IncTick   stm32f1xx_hal.o
    0x080021d0   0x080021d0   0x00000024   Code   RO          600    .text.HAL_Init      stm32f1xx_hal.o
    0x080021f4   0x080021f4   0x0000003c   Code   RO          602    .text.HAL_InitTick  stm32f1xx_hal.o
    0x08002230   0x08002230   0x00000040   Code   RO           74    .text.HAL_MspInit   stm32f1xx_hal_msp.o
    0x08002270   0x08002270   0x00000024   Code   RO          788    .text.HAL_NVIC_DisableIRQ  stm32f1xx_hal_cortex.o
    0x08002294   0x08002294   0x0000001c   Code   RO          786    .text.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x080022b0   0x080022b0   0x00000058   Code   RO          784    .text.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002308   0x08002308   0x00000020   Code   RO          782    .text.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08002328   0x08002328   0x000000fc   Code   RO          701    .text.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x08002424   0x08002424   0x00000134   Code   RO          666    .text.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08002558   0x08002558   0x00000014   Code   RO          678    .text.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x0800256c   0x0800256c   0x0000001c   Code   RO          680    .text.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08002588   0x08002588   0x00000058   Code   RO          668    .text.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x080025e0   0x080025e0   0x00000380   Code   RO          664    .text.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08002960   0x08002960   0x00000028   Code   RO          792    .text.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08002988   0x08002988   0x00000002   Code   RO         1056    .text.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x0800298a   0x0800298a   0x00000074   Code   RO         1008    .text.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x080029fe   0x080029fe   0x00000036   Code   RO          976    .text.HAL_UART_DeInit  stm32f1xx_hal_uart.o
    0x08002a34   0x08002a34   0x00000058   Code   RO          465    .text.HAL_UART_ErrorCallback  uart_dma.o
    0x08002a8c   0x08002a8c   0x000002a4   Code   RO         1048    .text.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002d30   0x08002d30   0x0000005e   Code   RO          964    .text.HAL_UART_Init  stm32f1xx_hal_uart.o
    0x08002d8e   0x08002d8e   0x00000002   PAD
    0x08002d90   0x08002d90   0x00000050   Code   RO           82    .text.HAL_UART_MspDeInit  stm32f1xx_hal_msp.o
    0x08002de0   0x08002de0   0x000001a8   Code   RO           80    .text.HAL_UART_MspInit  stm32f1xx_hal_msp.o
    0x08002f88   0x08002f88   0x000000be   Code   RO          984    .text.HAL_UART_Receive  stm32f1xx_hal_uart.o
    0x08003046   0x08003046   0x00000020   Code   RO         1000    .text.HAL_UART_Receive_DMA  stm32f1xx_hal_uart.o
    0x08003066   0x08003066   0x00000020   Code   RO          988    .text.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x08003086   0x08003086   0x00000002   PAD
    0x08003088   0x08003088   0x00000058   Code   RO          244    .text.HAL_UART_RxCpltCallback  modbus_slave.o
    0x080030e0   0x080030e0   0x00000002   Code   RO         1064    .text.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x080030e2   0x080030e2   0x000000ae   Code   RO          980    .text.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08003190   0x08003190   0x00000074   Code   RO          992    .text.HAL_UART_Transmit_DMA  stm32f1xx_hal_uart.o
    0x08003204   0x08003204   0x0000002c   Code   RO          463    .text.HAL_UART_TxCpltCallback  uart_dma.o
    0x08003230   0x08003230   0x00000002   Code   RO         1060    .text.HAL_UART_TxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08003232   0x08003232   0x00000002   PAD
    0x08003234   0x08003234   0x0000004c   Code   RO          210    .text.Handle_Communication_Error  modbus_master.o
    0x08003280   0x08003280   0x00000002   Code   RO           34    .text.HardFault_Handler  stm32f1xx_it.o
    0x08003282   0x08003282   0x00000002   PAD
    0x08003284   0x08003284   0x00000038   Code   RO          264    .text.I2C_Start     oled_display.o
    0x080032bc   0x080032bc   0x00000030   Code   RO          268    .text.I2C_Stop      oled_display.o
    0x080032ec   0x080032ec   0x0000007c   Code   RO          266    .text.I2C_WriteByte  oled_display.o
    0x08003368   0x08003368   0x00000018   Code   RO          296    .text.I2C_WriteBytes  oled_display.o
    0x08003380   0x08003380   0x00000038   Code   RO          155    .text.Key_Init      key_handler.o
    0x080033b8   0x080033b8   0x00000118   Code   RO          159    .text.Key_Process   key_handler.o
    0x080034d0   0x080034d0   0x0000014c   Code   RO          151    .text.Key_Scan_In_ISR  key_handler.o
    0x0800361c   0x0800361c   0x00000024   Code   RO          355    .text.LED_Control   system_status.o
    0x08003640   0x08003640   0x0000004c   Code   RO          359    .text.LED_Update    system_status.o
    0x0800368c   0x0800368c   0x00000002   Code   RO           36    .text.MemManage_Handler  stm32f1xx_it.o
    0x0800368e   0x0800368e   0x00000042   Code   RO          204    .text.Modbus_CRC16  modbus_master.o
    0x080036d0   0x080036d0   0x00000100   Code   RO          378    .text.Modbus_Extended_Init  modbus_extended.o
    0x080037d0   0x080037d0   0x0000006c   Code   RO          382    .text.Modbus_Extended_Process  modbus_extended.o
    0x0800383c   0x0800383c   0x00000016   Code   RO          198    .text.Modbus_Master_Init  modbus_master.o
    0x08003852   0x08003852   0x00000002   PAD
    0x08003854   0x08003854   0x00000118   Code   RO          202    .text.Modbus_Master_Process  modbus_master.o
    0x0800396c   0x0800396c   0x00000002   Code   RO          212    .text.Modbus_Master_Process_Response  modbus_master.o
    0x0800396e   0x0800396e   0x00000004   Code   RO          226    .text.Modbus_Slave_Init  modbus_slave.o
    0x08003972   0x08003972   0x00000004   Code   RO          230    .text.Modbus_Slave_Process  modbus_slave.o
    0x08003976   0x08003976   0x00000006   Code   RO          232    .text.Modbus_Slave_Process_Frame  modbus_slave.o
    0x0800397c   0x0800397c   0x00000002   Code   RO           32    .text.NMI_Handler   stm32f1xx_it.o
    0x0800397e   0x0800397e   0x00000002   PAD
    0x08003980   0x08003980   0x0000001c   Code   RO          300    .text.OLED_Clear    oled_display.o
    0x0800399c   0x0800399c   0x00000004   Code   RO          294    .text.OLED_Display_Process  oled_display.o
    0x080039a0   0x080039a0   0x00000020   Code   RO          274    .text.OLED_FrameBuffer_Init  oled_display.o
    0x080039c0   0x080039c0   0x00000040   Code   RO          280    .text.OLED_FrameBuffer_SetPixel  oled_display.o
    0x08003a00   0x08003a00   0x00000098   Code   RO          288    .text.OLED_FrameBuffer_Update  oled_display.o
    0x08003a98   0x08003a98   0x00000084   Code   RO          272    .text.OLED_Init     oled_display.o
    0x08003b1c   0x08003b1c   0x00000030   Code   RO          290    .text.OLED_SetDisplayWindow  oled_display.o
    0x08003b4c   0x08003b4c   0x00000100   Code   RO          302    .text.OLED_ShowChar  oled_display.o
    0x08003c4c   0x08003c4c   0x00000098   Code   RO          306    .text.OLED_ShowChinese  oled_display.o
    0x08003ce4   0x08003ce4   0x000000e8   Code   RO          304    .text.OLED_ShowString  oled_display.o
    0x08003dcc   0x08003dcc   0x00000024   Code   RO          262    .text.OLED_WriteCmd  oled_display.o
    0x08003df0   0x08003df0   0x0000002a   Code   RO          292    .text.OLED_WriteDataBurst  oled_display.o
    0x08003e1a   0x08003e1a   0x00000002   PAD
    0x08003e1c   0x08003e1c   0x00000028   Code   RO          208    .text.Parse_Response_Data  modbus_master.o
    0x08003e44   0x08003e44   0x00000002   Code   RO           46    .text.PendSV_Handler  stm32f1xx_it.o
    0x08003e46   0x08003e46   0x00000002   PAD
    0x08003e48   0x08003e48   0x000001ec   Code   RO          167    .text.Process_Advanced_Setting_Menu  key_handler.o
    0x08004034   0x08004034   0x000001d0   Code   RO          384    .text.Process_Extended_Frame  modbus_extended.o
    0x08004204   0x08004204   0x0000015c   Code   RO          165    .text.Process_Master_Setting_Menu  key_handler.o
    0x08004360   0x08004360   0x00000090   Code   RO          234    .text.Process_Modbus_Frame  modbus_slave.o
    0x080043f0   0x080043f0   0x00000090   Code   RO          161    .text.Process_Password_Menu  key_handler.o
    0x08004480   0x08004480   0x00000068   Code   RO          386    .text.Process_Read_All_Objects  modbus_extended.o
    0x080044e8   0x080044e8   0x000000e0   Code   RO          236    .text.Process_Read_Coils  modbus_slave.o
    0x080045c8   0x080045c8   0x000000ea   Code   RO          238    .text.Process_Read_Holding_Registers  modbus_slave.o
    0x080046b2   0x080046b2   0x00000002   PAD
    0x080046b4   0x080046b4   0x00000178   Code   RO          388    .text.Process_Read_Object  modbus_extended.o
    0x0800482c   0x0800482c   0x00000104   Code   RO          163    .text.Process_Slave_Setting_Menu  key_handler.o
    0x08004930   0x08004930   0x0000018c   Code   RO          390    .text.Process_Write_Object  modbus_extended.o
    0x08004abc   0x08004abc   0x00000154   Code   RO          240    .text.Process_Write_Single_Register  modbus_slave.o
    0x08004c10   0x08004c10   0x000000a0   Code   RO          200    .text.RS485_Set_Mode  modbus_master.o
    0x08004cb0   0x08004cb0   0x00000018   Code   RO          228    .text.RS485_Slave_Set_Mode  modbus_slave.o
    0x08004cc8   0x08004cc8   0x000000ac   Code   RO          242    .text.Read_Config_Register  modbus_slave.o
    0x08004d74   0x08004d74   0x0000004c   Code   RO          157    .text.Reset_Setting_Variables  key_handler.o
    0x08004dc0   0x08004dc0   0x00000002   Code   RO           42    .text.SVC_Handler   stm32f1xx_it.o
    0x08004dc2   0x08004dc2   0x00000002   PAD
    0x08004dc4   0x08004dc4   0x00000048   Code   RO          117    .text.Set_Moisture_Correction  config_manager.o
    0x08004e0c   0x08004e0c   0x00000044   Code   RO          115    .text.Set_Pressure_Correction  config_manager.o
    0x08004e50   0x08004e50   0x00000058   Code   RO          113    .text.Set_SF6_Threshold  config_manager.o
    0x08004ea8   0x08004ea8   0x0000002c   Code   RO          109    .text.Set_Slave_Address  config_manager.o
    0x08004ed4   0x08004ed4   0x00000030   Code   RO          111    .text.Set_Slave_Baudrate  config_manager.o
    0x08004f04   0x08004f04   0x00000058   Code   RO          455    .text.Split_Modbus_Frames  uart_dma.o
    0x08004f5c   0x08004f5c   0x00000024   Code   RO           48    .text.SysTick_Handler  stm32f1xx_it.o
    0x08004f80   0x08004f80   0x00000088   Code   RO           13    .text.SystemClock_Config  main.o
    0x08005008   0x08005008   0x00000002   Code   RO         1095    .text.SystemInit    system_stm32f1xx_1.o
    0x0800500a   0x0800500a   0x00000002   PAD
    0x0800500c   0x0800500c   0x00000020   Code   RO          334    .text.System_Check_Communication  system_monitor.o
    0x0800502c   0x0800502c   0x0000000c   Code   RO          336    .text.System_Get_Communication_Status  system_monitor.o
    0x08005038   0x08005038   0x0000001c   Code   RO          330    .text.System_Monitor_Init  system_monitor.o
    0x08005054   0x08005054   0x00000030   Code   RO          332    .text.System_Monitor_Update  system_monitor.o
    0x08005084   0x08005084   0x00000010   Code   RO          338    .text.System_Set_Communication_Status  system_monitor.o
    0x08005094   0x08005094   0x00000010   Code   RO          353    .text.System_Status_Init  system_status.o
    0x080050a4   0x080050a4   0x00000050   Code   RO          357    .text.System_Status_Update  system_status.o
    0x080050f4   0x080050f4   0x00000026   Code   RO          406    .text.TLV_Encode_DateTime  modbus_extended.o
    0x0800511a   0x0800511a   0x0000000a   Code   RO          451    .text.UART1_Frame_Received_Callback  uart_dma.o
    0x08005124   0x08005124   0x00000044   Code   RO          445    .text.UART1_Send_Data  uart_dma.o
    0x08005168   0x08005168   0x00000040   Code   RO          441    .text.UART1_Start_Receive  uart_dma.o
    0x080051a8   0x080051a8   0x00000040   Code   RO          443    .text.UART2_Start_Receive  uart_dma.o
    0x080051e8   0x080051e8   0x0000000c   Code   RO         1052    .text.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x080051f4   0x080051f4   0x0000004e   Code   RO          998    .text.UART_DMAError  stm32f1xx_hal_uart.o
    0x08005242   0x08005242   0x0000008c   Code   RO         1080    .text.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x080052ce   0x080052ce   0x00000018   Code   RO         1082    .text.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x080052e6   0x080052e6   0x0000003e   Code   RO          994    .text.UART_DMATransmitCplt  stm32f1xx_hal_uart.o
    0x08005324   0x08005324   0x00000006   Code   RO          996    .text.UART_DMATxHalfCplt  stm32f1xx_hal_uart.o
    0x0800532a   0x0800532a   0x00000002   PAD
    0x0800532c   0x0800532c   0x0000003c   Code   RO          439    .text.UART_DMA_Init  uart_dma.o
    0x08005368   0x08005368   0x000000c8   Code   RO          449    .text.UART_DMA_Process  uart_dma.o
    0x08005430   0x08005430   0x0000004e   Code   RO         1012    .text.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x0800547e   0x0800547e   0x0000001c   Code   RO         1010    .text.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x0800549a   0x0800549a   0x000000ca   Code   RO         1050    .text.UART_Receive_IT  stm32f1xx_hal_uart.o
    0x08005564   0x08005564   0x00000084   Code   RO          968    .text.UART_SetConfig  stm32f1xx_hal_uart.o
    0x080055e8   0x080055e8   0x00000090   Code   RO         1002    .text.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x08005678   0x08005678   0x00000032   Code   RO          990    .text.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x080056aa   0x080056aa   0x00000070   Code   RO          982    .text.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x0800571a   0x0800571a   0x00000002   PAD
    0x0800571c   0x0800571c   0x0000000c   Code   RO           50    .text.USART1_IRQHandler  stm32f1xx_it.o
    0x08005728   0x08005728   0x0000000c   Code   RO           52    .text.USART2_IRQHandler  stm32f1xx_it.o
    0x08005734   0x08005734   0x00000054   Code   RO          169    .text.Update_Menu_Display  key_handler.o
    0x08005788   0x08005788   0x00000090   Code   RO          420    .text.Update_SF6_Data_From_Modbus  modbus_extended.o
    0x08005818   0x08005818   0x00000002   Code   RO           40    .text.UsageFault_Handler  stm32f1xx_it.o
    0x0800581a   0x0800581a   0x00000002   PAD
    0x0800581c   0x0800581c   0x00000040   Code   RO          206    .text.Validate_Response  modbus_master.o
    0x0800585c   0x0800585c   0x0000003c   Code   RO          107    .text.Validate_Slave_Address  config_manager.o
    0x08005898   0x08005898   0x00000002   Code   RO          363    .text.Watchdog_Init  system_status.o
    0x0800589a   0x0800589a   0x00000002   PAD
    0x0800589c   0x0800589c   0x00000264   Code   RO           11    .text.main          main.o
    0x08005b00   0x08005b00   0x0000003e   Code   RO         1273    CL$$btod_d2e        c_w.l(btod.o)
    0x08005b3e   0x08005b3e   0x00000046   Code   RO         1275    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08005b84   0x08005b84   0x00000060   Code   RO         1274    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08005be4   0x08005be4   0x00000338   Code   RO         1283    CL$$btod_div_common  c_w.l(btod.o)
    0x08005f1c   0x08005f1c   0x000000dc   Code   RO         1280    CL$$btod_e2e        c_w.l(btod.o)
    0x08005ff8   0x08005ff8   0x0000002a   Code   RO         1277    CL$$btod_ediv       c_w.l(btod.o)
    0x08006022   0x08006022   0x0000002a   Code   RO         1276    CL$$btod_emul       c_w.l(btod.o)
    0x0800604c   0x0800604c   0x00000244   Code   RO         1282    CL$$btod_mult_common  c_w.l(btod.o)
    0x08006290   0x08006290   0x00000028   Code   RO         1302    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x080062b8   0x080062b8   0x0000000e   Code   RO         1157    i._is_digit         c_w.l(__printf_wp.o)
    0x080062c6   0x080062c6   0x00000002   PAD
    0x080062c8   0x080062c8   0x0000002c   Code   RO         1296    locale$$code        c_w.l(lc_numeric_c.o)
    0x080062f4   0x080062f4   0x0000000c   Code   RO         1231    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08006300   0x08006300   0x00000056   Code   RO         1185    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08006356   0x08006356   0x00000002   PAD
    0x08006358   0x08006358   0x000000c4   Code   RO         1187    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x0800641c   0x0800641c   0x00000054   Code   RO         1193    x$fpl$fcmp          fz_ws.l(fcmp.o)
    0x08006470   0x08006470   0x00000018   Code   RO         1298    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08006488   0x08006488   0x00000184   Code   RO         1196    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x0800660c   0x0800660c   0x00000068   Code   RO         1233    x$fpl$feqf          fz_ws.l(feqf.o)
    0x08006674   0x08006674   0x00000036   Code   RO         1199    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080066aa   0x080066aa   0x00000002   PAD
    0x080066ac   0x080066ac   0x0000003e   Code   RO         1203    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x080066ea   0x080066ea   0x00000002   PAD
    0x080066ec   0x080066ec   0x00000030   Code   RO         1208    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x0800671c   0x0800671c   0x00000026   Code   RO         1207    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08006742   0x08006742   0x00000002   PAD
    0x08006744   0x08006744   0x00000068   Code   RO         1235    x$fpl$fgeqf         fz_ws.l(fgeqf.o)
    0x080067ac   0x080067ac   0x00000068   Code   RO         1237    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08006814   0x08006814   0x00000102   Code   RO         1213    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08006916   0x08006916   0x0000008c   Code   RO         1239    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080069a2   0x080069a2   0x0000000a   Code   RO         1241    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080069ac   0x080069ac   0x000000ea   Code   RO         1189    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08006a96   0x08006a96   0x00000004   Code   RO         1215    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08006a9a   0x08006a9a   0x00000000   Code   RO         1243    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08006a9a   0x08006a9a   0x00000028   Data   RO         1130    .constdata          c_w.l(_printf_hex_int.o)
    0x08006ac2   0x08006ac2   0x00000011   Data   RO         1165    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08006ad3   0x08006ad3   0x00000001   PAD
    0x08006ad4   0x08006ad4   0x00000094   Data   RO         1271    .constdata          c_w.l(bigflt0.o)
    0x08006b68   0x08006b68   0x0000000c   Data   RO          184    .rodata..Lswitch.table.30  key_handler.o
    0x08006b74   0x08006b74   0x00000010   Data   RO         1100    .rodata.AHBPrescTable  system_stm32f1xx_1.o
    0x08006b84   0x08006b84   0x00000008   Data   RO         1101    .rodata.APBPrescTable  system_stm32f1xx_1.o
    0x08006b8c   0x08006b8c   0x000005b0   Data   RO          139    .rodata.ASCII_8x16  font.o
    0x0800713c   0x0800713c   0x000002d8   Data   RO          138    .rodata.ASCII_8x8   font.o
    0x08007414   0x08007414   0x00000929   Data   RO          140    .rodata.Chinese_16x16  font.o
    0x08007d3d   0x08007d3d   0x00000003   PAD
    0x08007d40   0x08007d40   0x00000010   Data   RO          183    .rodata.cst16       key_handler.o
    0x08007d50   0x08007d50   0x00000010   Data   RO          426    .rodata.cst16       modbus_extended.o
    0x08007d60   0x08007d60   0x00000044   Data   RO          182    .rodata.str1.1      key_handler.o
    0x08007da4   0x08007da4   0x00000020   Data   RO         1449    Region$$Table       anon$$obj.o
    0x08007dc4   0x08007dc4   0x0000001c   Data   RO         1295    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007de0, Size: 0x00001ad0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007de0   0x0000000c   Data   RW          650    .data..L_MergedGlobals  stm32f1xx_hal.o
    0x2000000c   0x08007dec   0x00000004   Data   RW         1099    .data.SystemCoreClock  system_stm32f1xx_1.o
    0x20000010        -       0x00000060   Zero   RW         1305    .bss                c_w.l(libspace.o)
    0x20000070        -       0x00000078   Zero   RW           19    .bss..L_MergedGlobals  main.o
    0x200000e8   0x08007df0   0x00000008   PAD
    0x200000f0        -       0x00000044   Zero   RW           84    .bss..L_MergedGlobals  stm32f1xx_hal_msp.o
    0x20000134   0x08007df0   0x0000000c   PAD
    0x20000140        -       0x0000004c   Zero   RW          185    .bss..L_MergedGlobals  key_handler.o
    0x2000018c        -       0x0000000c   Zero   RW          214    .bss..L_MergedGlobals  modbus_master.o
    0x20000198        -       0x00000008   Zero   RW          247    .bss..L_MergedGlobals  modbus_slave.o
    0x200001a0        -       0x0000000c   Zero   RW          366    .bss..L_MergedGlobals  system_status.o
    0x200001ac   0x08007df0   0x00000004   PAD
    0x200001b0        -       0x00000058   Zero   RW          427    .bss..L_MergedGlobals  modbus_extended.o
    0x20000208   0x08007df0   0x00000008   PAD
    0x20000210        -       0x00000048   Zero   RW           20    .bss..L_MergedGlobals.1  main.o
    0x20000258   0x08007df0   0x00000008   PAD
    0x20000260        -       0x00000044   Zero   RW           85    .bss..L_MergedGlobals.1  stm32f1xx_hal_msp.o
    0x200002a4   0x08007df0   0x0000000c   PAD
    0x200002b0        -       0x00000044   Zero   RW           86    .bss..L_MergedGlobals.2  stm32f1xx_hal_msp.o
    0x200002f4        -       0x00000008   Zero   RW          248    .bss..L_MergedGlobals.2  modbus_slave.o
    0x200002fc   0x08007df0   0x00000004   PAD
    0x20000300        -       0x00000044   Zero   RW           87    .bss..L_MergedGlobals.3  stm32f1xx_hal_msp.o
    0x20000344   0x08007df0   0x0000000c   PAD
    0x20000350        -       0x0000003c   Zero   RW          186    .bss..L_MergedGlobals.31  key_handler.o
    0x2000038c        -       0x00000004   Zero   RW           62    .bss.SysTick_Handler.key_scan_counter  stm32f1xx_it.o
    0x20000390        -       0x00000004   Zero   RW          469    .bss.UART_DMA_Process.last_check_time  uart_dma.o
    0x20000394        -       0x00000004   Zero   RW          341    .bss.channel_comm_status  system_monitor.o
    0x20000398        -       0x00000080   Zero   RW          125    .bss.coil_data      config_manager.o
    0x20000418        -       0x00000200   Zero   RW          124    .bss.corrected_data  config_manager.o
    0x20000618        -       0x00000001   Zero   RW          181    .bss.current_menu   key_handler.o
    0x20000619   0x08007df0   0x00000003   PAD
    0x2000061c        -       0x0000007c   Zero   RW          126    .bss.g_config       config_manager.o
    0x20000698        -       0x0000000c   Zero   RW          423    .bss.g_ext_debug    modbus_extended.o
    0x200006a4        -       0x00000410   Zero   RW          318    .bss.g_oled_fb      oled_display.o
    0x20000ab4        -       0x00000090   Zero   RW          422    .bss.g_sf6_data     modbus_extended.o
    0x20000b44        -       0x00000001   Zero   RW          365    .bss.g_system_state  system_status.o
    0x20000b45   0x08007df0   0x00000003   PAD
    0x20000b48        -       0x0000004c   Zero   RW          340    .bss.g_system_status  system_monitor.o
    0x20000b94        -       0x0000030c   Zero   RW          467    .bss.g_uart1_data   uart_dma.o
    0x20000ea0        -       0x0000030c   Zero   RW          468    .bss.g_uart2_data   uart_dma.o
    0x200011ac        -       0x00000004   Zero   RW           17    .bss.main.last_sf6_update  main.o
    0x200011b0        -       0x00000200   Zero   RW          123    .bss.modbus_data    config_manager.o
    0x200013b0        -       0x00000020   Zero   RW          896    .bss.pFlash         stm32f1xx_hal_flash.o
    0x200013d0        -       0x00000100   Zero   RW          246    .bss.rx_buffer      modbus_slave.o
    0x200014d0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x200016d0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       788         52          0          0       1276       5583   config_manager.o
         0          0       4529          0          0        820   font.o
      3512        526         96          0        137      10169   key_handler.o
       756         44          0          0        196      10015   main.o
      1886         98         16          0        244      10903   modbus_extended.o
       710         44          0          0         12       3987   modbus_master.o
      1240         80          0          0        272       8253   modbus_slave.o
      1470         48          0          0       1040      11345   oled_display.o
        64         26        236          0       1536        764   startup_stm32f103xb.o
       160         12          0         12          0       6476   stm32f1xx_hal.o
       634         28          0          0          0      10783   stm32f1xx_hal_adc.o
       224         28          0          0          0       8394   stm32f1xx_hal_cortex.o
       788         32          0          0          0       6639   stm32f1xx_hal_dma.o
       416         28          0          0         32       5450   stm32f1xx_hal_flash.o
       184          0          0          0          0       6446   stm32f1xx_hal_flash_ex.o
       684         56          0          0          0       4763   stm32f1xx_hal_gpio.o
       660         56          0          0        272       8140   stm32f1xx_hal_msp.o
      1340         72          0          0          0       7570   stm32f1xx_hal_rcc.o
       252          8          0          0          0       3594   stm32f1xx_hal_rcc_ex.o
      2558         32          0          0          0      23294   stm32f1xx_hal_uart.o
       124         28          0          0          4       2246   stm32f1xx_it.o
       136          8          0          0         80       2178   system_monitor.o
       210         16          0          0         13       2753   system_status.o
         2          0         24          4          0       1703   system_stm32f1xx_1.o
       686         48          0          0       1564       7506   uart_dma.o

    ----------------------------------------------------------------------
     19522       <USER>       <GROUP>         16       6752     169774   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          3          0         74          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        84          0          0          0          0        196   fcmp.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
       104          4          0          0          0         84   feqf.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fgeqf.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      7532        <USER>        <GROUP>          0         96       4896   Library Totals
        16          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5526        214        233          0         96       3288   c_w.l
      1950        116          0          0          0       1540   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      7532        <USER>        <GROUP>          0         96       4896   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27054       1700       5170         16       6848     171558   Grand Totals
     27054       1700       5170         16       6848     171558   ELF Image Totals
     27054       1700       5170         16          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                32224 (  31.47kB)
    Total RW  Size (RW Data + ZI Data)              6864 (   6.70kB)
    Total ROM Size (Code + RO Data + RW Data)      32240 (  31.48kB)

==============================================================================

