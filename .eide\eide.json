{"name": "OMS1001TEST", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f103xb.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/stm32f1xx_it.c"}, {"path": "../Core/Src/stm32f1xx_hal_msp.c"}, {"path": "../Core/Src/config_manager.c"}, {"path": "../Core/Src/font.c"}, {"path": "../Core/Src/key_handler.c"}, {"path": "../Core/Src/modbus_master.c"}, {"path": "../Core/Src/modbus_slave.c"}, {"path": "../Core/Src/oled_display.c"}, {"path": "../Core/Src/system_monitor.c"}, {"path": "../Core/Src/system_status.c"}, {"path": "../Core/Src/modbus_extended.c"}, {"path": "../Core/Src/uart_dma.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F1xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f1xx.c"}], "folders": []}]}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "8da6910e97f2f48220e7973fc6ce83ee"}, "targets": {"OMS1001TEST": {"excludeList": [], "toolchain": "AC6", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_OMS1001TEST"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F103xB"]}, "builderOptions": {"AC6": {"version": 3, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-image-size", "language-c": "c99", "language-cpp": "c++11", "link-time-optimization": false, "one-elf-section-per-function": true, "short-enums#wchar": true, "warnings": "ac5-like-warnings"}, "asm-compiler": {"$use": "asm", "misc-controls": ""}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "misc-controls": "--diag_suppress=L6329", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.6"}