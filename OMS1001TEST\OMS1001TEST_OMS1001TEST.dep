Dependencies for Project 'OMS1001TEST', Target 'OMS1001TEST': (DO NOT MODIFY !)
F (startup_stm32f103xb.s)(0x68806B5A)(--cpu Cortex-M3 -g

-I.\RTE\_OMS1001TEST

-I"D:\Program Files\Keil_v5\ARM\PACK\ARM\CMSIS\5.0.1\CMSIS\Include"

-I"D:\Program Files\Keil_v5\ARM\PACK\Keil\STM32F1xx_DFP\2.3.0\Device\Include"

--pd "__UVISION_VERSION SETA 524"

--pd "_RTE_ SETA 1"

--pd "STM32F10X_MD SETA 1"

--list startup_stm32f103xb.lst

--xref -o oms1001test\startup_stm32f103xb.o

--depend oms1001test\startup_stm32f103xb.d)
F (../Core/Src/main.c)(0x688AFCAB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/main.o -MD)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\modbus_master.h)(0x688AFEA6)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
I (..\Core\Inc\modbus_slave.h)(0x688AFE77)
I (..\Core\Inc\modbus_extended.h)(0x688A5261)
I (..\Core\Inc\oled_display.h)(0x68822827)
I (..\Core\Inc\key_handler.h)(0x68826AF6)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\system_status.h)(0x6880876E)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (..\Core\Inc\font.h)(0x6882350B)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
F (../Core/Src/stm32f1xx_it.c)(0x688AFCBC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_it.o -MD)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_it.h)(0x68806B58)
I (..\Core\Inc\key_handler.h)(0x68826AF6)
F (../Core/Src/stm32f1xx_hal_msp.c)(0x688AFCF0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_msp.o -MD)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (..\Core\Src\config_manager.c)(0x688A42AF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/config_manager.o -MD)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
F (..\Core\Src\font.c)(0x688271CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/font.o -MD)
I (..\Core\Inc\font.h)(0x6882350B)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (..\Core\Src\key_handler.c)(0x688A412A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/key_handler.o -MD)
I (..\Core\Inc\key_handler.h)(0x68826AF6)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\oled_display.h)(0x68822827)
I (..\Core\Inc\modbus_master.h)(0x688AFEA6)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (..\Core\Inc\font.h)(0x6882350B)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
F (..\Core\Src\modbus_master.c)(0x688B0691)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/modbus_master.o -MD)
I (..\Core\Inc\modbus_master.h)(0x688AFEA6)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (..\Core\Inc\uart_dma.h)(0x688AFD71)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
F (..\Core\Src\modbus_slave.c)(0x688B0000)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/modbus_slave.o -MD)
I (..\Core\Inc\modbus_slave.h)(0x688AFE77)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\modbus_extended.h)(0x688A5261)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
F (..\Core\Src\oled_display.c)(0x68826BFD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/oled_display.o -MD)
I (..\Core\Inc\oled_display.h)(0x68822827)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\font.h)(0x6882350B)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
F (..\Core\Src\system_monitor.c)(0x68809472)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/system_monitor.o -MD)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
F (..\Core\Src\system_status.c)(0x68808784)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/system_status.o -MD)
I (..\Core\Inc\system_status.h)(0x6880876E)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\system_monitor.h)(0x68809475)
F (..\Core\Src\modbus_extended.c)(0x688AFE35)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/modbus_extended.o -MD)
I (..\Core\Inc\modbus_extended.h)(0x688A5261)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\config_manager.h)(0x688A40E5)
I (..\Core\Inc\system_monitor.h)(0x68809475)
I (..\Core\Inc\uart_dma.h)(0x688AFD71)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x588CAFD2)
F (..\Core\Src\uart_dma.c)(0x688B0677)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/uart_dma.o -MD)
I (..\Core\Inc\uart_dma.h)(0x688AFD71)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
I (..\Core\Inc\modbus_slave.h)(0x688AFE77)
I (..\Core\Inc\main.h)(0x68808913)
I (..\Core\Inc\modbus_extended.h)(0x688A5261)
I (..\Core\Inc\modbus_master.h)(0x688AFEA6)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\string.h)(0x588CAFD2)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_gpio_ex.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_adc.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_adc_ex.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_rcc.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_rcc_ex.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_gpio.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_dma.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_cortex.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_pwr.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_flash.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_flash_ex.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_exti.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/stm32f1xx_hal_uart.o -MD)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
F (../Core/Src/system_stm32f1xx.c)(0x680B474D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-3 -Oz -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_OMS1001TEST

-I"D:/Program Files/Keil_v5/ARM/PACK/ARM/CMSIS/5.0.1/CMSIS/Include"

-I"D:/Program Files/Keil_v5/ARM/PACK/Keil/STM32F1xx_DFP/2.3.0/Device/Include"

-D__UVISION_VERSION="524" -D_RTE_ -DSTM32F10X_MD -DUSE_HAL_DRIVER -DSTM32F103xB

-o oms1001test/system_stm32f1xx_1.o -MD)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h)(0x680B474D)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xb.h)(0x680B474D)
I (..\Drivers\CMSIS\Include\core_cm3.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x588CAFD2)
I (..\Drivers\CMSIS\Include\cmsis_version.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x680B4736)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x680B4736)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x57FC022C)
I (..\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h)(0x680B474D)
I (..\Core\Inc\stm32f1xx_hal_conf.h)(0x68807DC2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x680B474D)
I (D:\Program Files\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x588CAFD2)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_adc_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_usart.h)(0x680B474D)
I (..\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_wwdg.h)(0x680B474D)
